using System.Collections.Generic;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data.constraction;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Component;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.Framework.Event;
using WizardGames.Soc.SocWorld.Lobby;
using WizardGames.SocConst.Soc.Const;
using ConstructionPartType = WizardGames.Soc.Common.Construction.ConstructionPartType;
using DamageType = WizardGames.Soc.Common.Data.Combat.DamageType;

namespace WizardGames.Soc.Common.Component
{
    [HotfixClass]
    public static partial class TerritoryPlunderComponentHotfix
    {
        static TerritoryPlunderComponentHotfix()
        {
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<DestroyPartEvent>((pe, e) =>
            {
                var terrEnt = TerritoryManagerEntity.Instance.GetTerritoryEntity(e.TerritoryId);
                if (terrEnt == null) return;
                if (terrEnt.CheckPlunderReportStartOnAttacked(pe, 0, true))
                {
                    // 不会为null
                    terrEnt.PlunderComp.OnDestroyConstructionEvent(e);
                }
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<AttackPartEvent>((pe, e) =>
            {
                var terrEnt = TerritoryManagerEntity.Instance.GetTerritoryEntity(e.TerritoryId);
                if (terrEnt == null) return;
                if (terrEnt.CheckPlunderReportStartOnAttacked(pe, e.Damage, e.IsDestroy))
                {
                    terrEnt.PlunderComp.OnAttackPartEvent(e);
                }
            });
        }


        const int MAX_FLOW_DATA_COUNT = 200; // 流水数据最大数量
        static ConstList<(long, int)> INDEX_LIST = new(
            (TerritoryPlunderContainerNodeIndex.Kill, BattleSummaryDataType.KILL_TIMES),
            (TerritoryPlunderContainerNodeIndex.Death, BattleSummaryDataType.DEATH_TIMES),
            (TerritoryPlunderContainerNodeIndex.Damage, BattleSummaryDataType.DAMAGE));
        /// <summary>
        /// 需要记录到流水里的伤害类型
        /// </summary>
        private static ConstHashSet<int> FLOW_DATA_DAMAGE_TYPE = new ConstHashSet<int>(
            (int)DamageType.Cold,
            (int)DamageType.Heat,
            (int)DamageType.Hunger,
            (int)DamageType.Radiation,
            (int)DamageType.Fall);

        /// <summary>
        /// 需要计数的伤害类型 对应任务@伤害条件表的主键
        /// </summary>
        private static readonly List<long> NEED_COUNT_DAMAGE_SELECT_ID = new List<long>() { 1001 };

        /// <summary>
        /// 返回是否为sourcePlayer第一次加入到本领地的抄家
        /// </summary>
        public static bool CheckPlunderReportStartOnAttacked(this TerritoryEntity self, PlayerEntity sourcePlayer, float realDamage, bool isDead)
        {
            if (FunctionSwitchComponent.Instance.IsDisable(FunctionConst.Plunder))
            {
                return false;
            }

            var plunderComp = self.PlunderComp;
            if (plunderComp == null)
            {
                // 说明领地柜已经没了，只延续已经有的战报数据，不用触发新的战报
                if (self.PlayComp.State != ETerritoryState.Normal)
                    return false;
                if (self.PlayComp.EnterAttackedTimerId == TimerWheelConst.INVALID_TIMER_ID)
                    return false;
                self.Logger.Info($"Add Components on attacked.");
                // 首次遇袭，初始化组件
                var rootNode = new RootNodeComponent();
                self.AddComponent(rootNode);
                plunderComp = new TerritoryPlunderComponent();
                self.AddComponent(plunderComp);
            }

            plunderComp.TryStartPlunderReport(sourcePlayer, realDamage, isDead);

            var ret = false;
            if (!plunderComp.fightingPlayers.Contains(sourcePlayer.EntityId))
            {
                plunderComp.AddFightingPlayer(sourcePlayer);
                ret = true;
                var team = sourcePlayer.MyTeam;
                if (team != null)
                    plunderComp.AddFightingTeam(team);
            }
            return ret;

        }
        private static void TryStartPlunderReport(this TerritoryPlunderComponent self, PlayerEntity sourcePlayer, float realDamage, bool isDead)
        {
            var terrEnt = self.ParentEntity as TerritoryEntity;
            if (self.PlunderTs == 0)
            {
                self.plunderDamage += Mathf.Abs(realDamage);
                self.Logger.Debug($"[StartPlunderReport] sourcePlayerId {sourcePlayer.EntityId} realDamage {realDamage}");
                if (self.plunderDamage >= McCommon.Tables.TbPlunderConst.PlunderDamageHp || isDead)
                {
                    self.Logger.Info($"[StartPlunderReport] start record data now");
                    self.PlunderTs = ProcessEntity.Instance.NowTs;
                    var creatorPlayer = UserManagerEntity.Instance.GetPlayerEntity(terrEnt.BaseComp.CreatorRoleId);
                    if (creatorPlayer != null)
                    {
                        var timeout = (uint)McCommon.Tables.TbPlunderConst.PlunderTipCD * 1000;
                        var tId = self.AddTimerOnce(timeout, OnPlunderNoticeTimerHotfix);
                        self.Logger.Info($"[StartPlunderReport] start plunder notice timer {tId} timeout {timeout}");
                        self.AddFightingPlayer(creatorPlayer);
                        var team = creatorPlayer.MyTeam;
                        if (team != null)
                            self.AddFightingTeam(team);
                    }
                }
            }
        }
        public static void CancelWaitClosePlunderTimer(this TerritoryPlunderComponent self)
        {
            if (self.waitClosePlunderTimer == TimerWheelConst.INVALID_TIMER_ID)
            {
                return;
            }
            self.Logger.Info($"[CancelWaitClosePlunderTimer] cancel wait close plunder, timer {self.waitClosePlunderTimer}");
            self.SafeCancelTimer(ref self.waitClosePlunderTimer);
        }
        public static void OnAttackStopped(this TerritoryPlunderComponent self)
        {
            var timeout = (uint)(McCommon.Tables.TbPlunderConst.PlunderCD * 1000);
            self.waitClosePlunderTimer = self.AddTimerOnce(timeout, OnWaitClosePlunderHotfix);
            self.Logger.Info($"[OnAttackStopped] wait to close plunder result, timer {self.waitClosePlunderTimer} timeout {timeout}");
        }

        [Hotfix(TimerCallback = true)]
        private static void OnWaitClosePlunder(this TerritoryPlunderComponent self, long timerId)
        {
            self.Logger.Info($"[OnWaitClosePlunder] {timerId}");
            self.waitClosePlunderTimer = TimerWheelConst.INVALID_TIMER_ID;
            self.CloseReport();
        }

        [Hotfix(TimerCallback = true)]
        private static void OnPlunderNoticeTimer(this TerritoryPlunderComponent self, long timerId)
        {
            var terrEnt = self.ParentEntity as TerritoryEntity;
            var creatorRoleId = terrEnt.BaseComp.CreatorRoleId;
            var creatorPlayer = UserManagerEntity.Instance.GetPlayerEntity(creatorRoleId);
            if (creatorPlayer == null)
            {
                self.Logger.Error($"[OnPlunderNoticeTimer] playe not found, creatorRoleId {creatorRoleId}");
                return;
            }

            SetPlundering(creatorPlayer, terrEnt, true);
            var territoryPos = new SimpleVector3(terrEnt.PosX, terrEnt.PosY, terrEnt.PosZ);
            self.SendPlunderNotice(creatorPlayer, terrEnt.EntityId, territoryPos);
            var team = creatorPlayer.MyTeam;
            if (team != null)
            {
                foreach (var member in team.GetPlayerMember())
                {
                    if (member.RoleId == creatorPlayer.RoleId)
                        continue;
                    self.SendPlunderNotice(member, terrEnt.EntityId, territoryPos);
                }
            }
        }
        private static void SetPlundering(PlayerEntity creatorPlayer, TerritoryEntity terrEnt, bool isPlundering)
        {
            var creatorBriefInfo = creatorPlayer.ConstructionComp.BriefBPInfo[terrEnt.EntityId];
            if (creatorBriefInfo.TerritoryInfo != null)
                creatorBriefInfo.TerritoryInfo.IsPlundering = isPlundering;
            foreach (var roleId in terrEnt.PermissionComp.GetAllMemebers())
            {
                var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
                var briefInfo = player.ConstructionComp.BriefBPInfo[terrEnt.EntityId];
                if (briefInfo.TerritoryInfo != null)
                    briefInfo.TerritoryInfo.IsPlundering = isPlundering;
            }
        }

        private static void SendPlunderNotice(this TerritoryPlunderComponent self, PlayerEntity player, long terrId, SimpleVector3 terrPos)
        {
            if (player.CurrentNetPeerId != NetConst.INVALID_NET_PEER_ID)
            {
                var defenderPos = new Vector3(player.PosX, player.PosY, player.PosZ);
                var territoryPos = terrPos.ToVector3();
                bool isInTerritory = BuildingPrivilegeHelper.IsInChargePos(ref defenderPos, ref territoryPos);
                if (isInTerritory)
                {
                    self.Logger.Info($"[OnPlunderNoticeTimer] ignored online tip to player {player.RoleId} in territory");
                    return;
                }
                var playerPlunderComp = player.GetComponent<PlayerPlunderComponent>(EComponentIdEnum.PlayerPlunder);
                playerPlunderComp.RemoteCallPlunderShouldGoBackDefence(ERpcTarget.OwnClient, terrId, terrPos);
                self.Logger.Info($"[OnPlunderNoticeTimer] send online tip to player {player.RoleId}");
            }
            else
            {
                LobbyServiceManager.Instance.lobbyService.PushAppMessage(player.RoleId,AppPushTipConst.PlunderShouldGoBackDefence);
                self.Logger.Info($"[OnPlunderNoticeTimer] send offline tip to player {player.RoleId}");
            }
        }

        internal static void AddFightingPlayer(this TerritoryPlunderComponent self, PlayerEntity player)
        {
            if (self.fightingPlayers.Contains(player.EntityId))
                return;
            self.Logger.Info($"AddFightingPlayer entityId {player.EntityId} roleId {player.RoleId}");
            self.fightingPlayers.Add(player.EntityId);
            self.DelayExecute(() =>
            {
                self.RegisterEvent(player);
            });

        }

        private static void AddFightingTeam(this TerritoryPlunderComponent self, TeamEntity team)
        {
            if (self.fightingTeams.Contains(team.EntityId))
                return;
            self.Logger.Info($"AddFightingTeam teamId {team.EntityId}");
            self.fightingTeams.Add(team.EntityId);
            self.ListenTargetEvent(team.EntityId, OnAfterJoinTeamHotfix);

            foreach (var member in team.GetPlayerMember())
            {
                self.AddFightingPlayer(member);
            }
        }
        [Hotfix(EventCallback = true)]
        private static void OnPlayerCreateTeam(this TerritoryPlunderComponent self, PlayerCreateTeamEvent evt)
        {
            var player = UserManagerEntity.Instance.GetPlayerEntity(evt.RoleId);
            if (player == null)
            {
                self.Logger.Error($"OnPlayerCreateTeam player not found, roleId {evt.RoleId}");
                return;
            }
            if (self.fightingTeams.Contains(player.TeamId))
                return;
            var team = player.MyTeam;
            self.Logger.Info($"OnPlayerCreateTeam roleId {player.RoleId} teamId {team.EntityId} {team.MemberDic.Count}");
            self.AddFightingTeam(team);
        }
        [Hotfix(EventCallback = true)]
        private static void OnAfterJoinTeam(this TerritoryPlunderComponent self, JoinTeamWithPlayerEntity evt)
        {
            var player = evt.JoinPlayer;
            if (player == null)
            {
                self.Logger.Error($"OnAfterJoinTeam player not found, roleId {player.RoleId}");
                return;
            }
            if (self.fightingPlayers.Contains(player.EntityId))
                return;
            self.Logger.Info($"OnAfterJoinTeam roleId {player.RoleId} teamId {player.TeamId}");
            self.AddFightingPlayer(player);
        }
        private static void ListenPlayerEvent(this TerritoryPlunderComponent self, PlayerEntity player)
        {

        }

        [Hotfix(EventCallback = true)]
        private static void OnPlayerDie(this TerritoryPlunderComponent self, SelfDieEvent evt)
        {
            if (evt.DamageData == null)
                return; // 自杀不算
            var player = EntityManager.Instance.GetPlayerEntity(evt.DeadPlayerId);
            self.AddPlayerFlowData(player, evt.DamageData, true);
        }

        [Hotfix(EventCallback = true)]
        private static void OnPlayerDown(this TerritoryPlunderComponent self, SelfDownEvent evt)
        {
            var player = EntityManager.Instance.GetPlayerEntity(evt.DownPlayerId);
            self.AddPlayerFlowData(player, evt.DamageData, false);
        }

        [Hotfix(EventCallback = true)]
        private static void OnDestroyPart(this TerritoryPlunderComponent self, DestroyPartEvent evt)
        {
            if (self.ParentId != evt.TerritoryId)
                return;
            var player = EntityManager.Instance.GetPlayerEntity(evt.EntityId);
            var flowData = new PlunderConstructionDestroyFlowData
            {
                OccurrentSec = ProcessEntity.Instance.NowSec,
                SourceRoleId = player.RoleId,
                TemplateId = evt.BizId,
                Grade = evt.Grade,
                WeaponTemplateId = evt.WeaponTableId
            };
            self.InputFlowData(flowData);
        }

        private static void AddPlayerFlowData(this TerritoryPlunderComponent self, PlayerEntity player, DamageData data, bool isKilled)
        {
            var sourceId = data.SourcePlayerId != 0 ? data.SourcePlayerId : data.SourceId;
            var sourceEntity = EntityManager.Instance.GetEntity(sourceId);

            if (sourceEntity is PlayerEntity sourcePlayer)
            {
                var flowData = new PlunderPlayerFlowData();
                flowData.OccurrentSec = ProcessEntity.Instance.NowSec;
                flowData.SourceRoleId = sourcePlayer.RoleId;
                flowData.TargetRoleId = player.RoleId;
                flowData.WeaponTemplateId = data.WeaponTableId;
                flowData.IsKilled = isKilled;
                self.InputFlowData(flowData);
                self.Logger.Info($"AddPlayerFlowData isKilled {isKilled} targetPlayer {player.RoleId} sourcePlayer {sourcePlayer.RoleId}");
                return;
            }
            if (sourceEntity is PartEntity sourcePart)
            {
                if (sourcePart.TerritoryId == self.ParentId)
                {
                    // 陷阱
                    var flowData = new PlunderAIDamageFlowData();
                    flowData.OccurrentSec = ProcessEntity.Instance.NowSec;
                    flowData.IsKilled = isKilled;
                    flowData.TargetRoleId = player.RoleId;
                    flowData.TemplateId = sourcePart.TemplateId;
                    self.InputFlowData(flowData);
                    self.Logger.Info($"AddPlayerFlowData isKilled {isKilled} targetPlayer {player.RoleId} trap {sourcePart.TemplateId}");
                    return;
                }
            }
            if (sourceEntity is MonsterEntity sourceMonster)
            {
                // AI
                if (sourceMonster.PartEntityId != 0)
                {
                    var part = EntityManager.Instance.GetEntity(sourceMonster.PartEntityId) as PartEntity;
                    if (part != null && part.TerritoryId == self.ParentId)
                    {
                        var flowData = new PlunderAIDamageFlowData();
                        flowData.OccurrentSec = ProcessEntity.Instance.NowSec;
                        flowData.IsKilled = isKilled;
                        flowData.TargetRoleId = player.RoleId;
                        flowData.TemplateId = sourceMonster.TemplateId;
                        self.InputFlowData(flowData);
                        self.Logger.Info($"AddPlayerFlowData isKilled {isKilled} targetPlayer {player.RoleId} ai {sourceMonster.TemplateId}");
                        return;
                    }
                }

            }

            // BUFF
            if (FLOW_DATA_DAMAGE_TYPE.Contains(data.DamageType))
            {
                var flowData = new PlunderBuffFlowData();
                flowData.OccurrentSec = ProcessEntity.Instance.NowSec;
                flowData.IsKilled = isKilled;
                flowData.BuffType = data.DamageType;
                flowData.TargetRoleId = player.RoleId;
                self.InputFlowData(flowData);
                self.Logger.Info($"AddPlayerFlowData isKilled {isKilled} targetPlayer {player.RoleId} buff {data.DamageType}");
                return;
            }
        }
        private static void InputFlowData(this TerritoryPlunderComponent self, PlunderFlowData data)
        {
            self.flowDataQueue.Enqueue(data);
            if (self.flowDataQueue.Count > MAX_FLOW_DATA_COUNT)
            {
                self.flowDataQueue.Dequeue();
            }
        }
        [Hotfix(EventCallback = true)]
        private static void OnBeforeServerStopEvent(this TerritoryPlunderComponent self, BeforeServerStopEvent evt)
        {
            self.Logger.Info($"OnBeforeServerStopEvent");
            self.CloseReport();
        }

        public static void CloseReport(this TerritoryPlunderComponent self)
        {
            var terrEnt = self.ParentEntity as TerritoryEntity;
            var creatorPlayer = UserManagerEntity.Instance.GetPlayerEntity(terrEnt.BaseComp.CreatorRoleId);
            if (creatorPlayer != null)
                SetPlundering(creatorPlayer, terrEnt, false);
            if (self.PlunderTs == 0)
            {
                self.Logger.Info($"CloseReport no plunder data, just remove component.");
                // 清理攻击时间跟踪字典
                self.lastPlayerAttackTime.Clear();
                terrEnt.RemoveComponent(self.Id);
                terrEnt.RemoveComponent(EComponentIdEnum.RootNodeComponent);
                return;
            }
            bool isDefendSuccess = (terrEnt.PlayComp.State == ETerritoryState.Normal);
            var defenderReportId = ++TerritoryManagerEntity.Instance.PlunderReportCount;
            // 流水
            var flowDataSet = self.CreateFlowDataSet(defenderReportId);

            List<long> defenderPlayers = new(4); // 防守方
            // 防守方战报，可能为空
            PlunderReport defenderReport = null;
            if (creatorPlayer != null)
            {
                defenderPlayers.Add(creatorPlayer.EntityId);
                var defenderTeam = creatorPlayer.MyTeam;
                if (defenderTeam != null)
                {
                    foreach (var member in defenderTeam.GetPlayerMember())
                    {
                        if (member.EntityId == creatorPlayer.EntityId) continue;
                        defenderPlayers.Add(member.EntityId);
                    }
                }

                defenderReport = self.GenNewReport(defenderReportId, isDefendSuccess, true, flowDataSet);
                self.RecordDefenderBattleSummary(creatorPlayer, defenderReport, defenderPlayers);
            }

            HashSet<long> teamIdSet = new(); // 相同组队情况，只计算一次
            foreach (var playerId in self.fightingPlayers)
            {
                if (creatorPlayer != null && creatorPlayer.EntityId == playerId)
                    continue;
                var player = EntityManager.Instance.GetPlayerEntity(playerId);
                if (creatorPlayer != null && creatorPlayer.TeamId != 0 && creatorPlayer.TeamId == player.TeamId)
                    continue;
                if (player.TeamId != 0)
                {
                    if (teamIdSet.Contains(player.TeamId))
                    {
                        continue;
                    }
                    teamIdSet.Add(player.TeamId);
                }

                PlunderReport attackerReport = null;
                var reportId = ++TerritoryManagerEntity.Instance.PlunderReportCount;
                attackerReport = self.GenNewReport(reportId, isDefendSuccess, false, flowDataSet);
                self.CopyDefenderBattleData(defenderReport, attackerReport);
                self.RecordAttackData(player, defenderReport, attackerReport, defenderPlayers);
                self.AddResultToPlayer(player, attackerReport);
                TerritoryManagerEntity.Instance.PlunderReportDict.Add(attackerReport.ReportId, attackerReport);
            }
            if (creatorPlayer != null)
            {
                self.AddResultToPlayer(creatorPlayer, defenderReport);
                TerritoryManagerEntity.Instance.PlunderReportDict.Add(defenderReport.ReportId, defenderReport);
            }
            TerritoryManagerEntity.Instance.PlunderFlowDataSetDict.Add(flowDataSet.FlowId, flowDataSet);
            self.Logger.Info($"CloseReport flowDataSet {flowDataSet.FlowId} {flowDataSet.RefCount}");

            // 清理攻击时间跟踪字典
            self.lastPlayerAttackTime.Clear();

            terrEnt.RemoveComponent(self.Id);
            terrEnt.RemoveComponent(EComponentIdEnum.RootNodeComponent);
        }
        private static PlunderReport GenNewReport(this TerritoryPlunderComponent self, long reportId, bool isDefendSuccess, bool isDefenderReport, PlunderFlowDataSet flowDataSet)
        {
            var terrEnt = self.ParentEntity as TerritoryEntity;
            var ret = new PlunderReport();
            ret.ReportId = reportId;
            ret.FlowId = flowDataSet.FlowId;
            ret.IsDefenerReport = isDefenderReport;
            ret.TerritoryName = terrEnt.RenameComp.ConstructionName;
            ret.PlunderTs = self.PlunderTs;
            ret.PlunderCostSec = (int)(ProcessEntity.Instance.NowTs - self.PlunderTs);
            ret.IsDefendSuccess = isDefendSuccess;
            flowDataSet.RefCount++;
            return ret;
        }

        private static void AddResultToPlayer(this TerritoryPlunderComponent self, PlayerEntity player, PlunderReport report)
        {
            var team = player.MyTeam;
            if (team == null)
            {
                var plunderComp = player.GetComponent<PlayerPlunderComponent>(EComponentIdEnum.PlayerPlunder);
                plunderComp.AddResult(report);
            }
            else
            {
                foreach (var member in team.GetPlayerMember())
                {
                    var plunderComp = member.GetComponent<PlayerPlunderComponent>(EComponentIdEnum.PlayerPlunder);
                    plunderComp.AddResult(report);
                }
            }
        }

        private static PlunderFlowDataSet CreateFlowDataSet(this TerritoryPlunderComponent self, long defenderReportId)
        {
            var dataSet = new PlunderFlowDataSet();
            dataSet.FlowId = defenderReportId;
            dataSet.FlowData = new();
            foreach (var data in self.flowDataQueue)
            {
                dataSet.FlowData.Add(data);
            }
            self.flowDataQueue.Clear();
            return dataSet;
        }

        private static void RecordDefenderBattleSummary(this TerritoryPlunderComponent self, PlayerEntity creatorPlayer, PlunderReport defenderReport, List<long> defenderPlayers)
        {
            var team = creatorPlayer.MyTeam;
            if (team == null)
            {
                defenderReport.DefenderData[creatorPlayer.RoleId] = CreateBaseSummary(self, creatorPlayer, defenderPlayers);
            }
            else
            {
                foreach (var member in team.GetPlayerMember())
                {
                    defenderReport.DefenderData[member.RoleId] = CreateBaseSummary(self, member, defenderPlayers);
                }
            }
        }
        private static void CopyDefenderBattleData(this TerritoryPlunderComponent self, PlunderReport defenderReport, PlunderReport attackerReport)
        {
            if (defenderReport == null)
                return;
            foreach (var (roleId, roleData) in defenderReport.DefenderData)
            {
                attackerReport.DefenderData[roleId] = roleData.Clone();
            }
        }

        private static void RecordAttackData(this TerritoryPlunderComponent self, PlayerEntity player, PlunderReport defenderReport, PlunderReport attackerReport, List<long> defenderPlayers)
        {
            var team = player.MyTeam;
            if (team == null)
            {
                self.RecordOneAttackerData(player, defenderReport, attackerReport, defenderPlayers);
            }
            else
            {
                foreach (var member in team.GetPlayerMember())
                {
                    self.RecordOneAttackerData(member, defenderReport, attackerReport, defenderPlayers);
                }
            }
        }
        private static void RecordOneAttackerData(this TerritoryPlunderComponent self, PlayerEntity attacker, PlunderReport defenderReport, PlunderReport attackerReport, List<long> defenderPlayers)
        {
            var battleSummary = self.CreateBaseSummary(attacker, defenderPlayers);
            self.RecordExplosive(attacker, battleSummary);
            self.RecordDestroyedFurnishing(attacker, battleSummary);
            self.RecordDestroyedConstruction(attacker, battleSummary);
            self.RecordPlunderItems(attacker, battleSummary, defenderReport, attackerReport);
            attackerReport.AttackerData[attacker.RoleId] = battleSummary;
            if (defenderReport != null)
                defenderReport.AttackerData[attacker.RoleId] = battleSummary.Clone();
        }

        private static BattleSummary CreateBaseSummary(this TerritoryPlunderComponent self, PlayerEntity player, List<long> defenderPlayers)
        {
            var ret = new BattleSummary();
            foreach (var (containerIndex, dataType) in INDEX_LIST)
            {
                var playerContainer = self.SystemRootNode.GetNodeByRelativePath(player.EntityId, containerIndex) as DirectoryNode;
                if (playerContainer == null)
                {
                    self.Logger.Info($"CreateBaseSummary container not found. playerId {player.EntityId}, index {containerIndex}");
                    continue;
                }
                foreach (var (idx, node) in playerContainer)
                {
                    if (node is not IntNode intNode)
                    {
                        self.Logger.Error($"CreateBaseSummary node is no cloneable. {player.RoleId} {node}");
                        continue;
                    }
                    if (containerIndex == TerritoryPlunderContainerNodeIndex.Kill)
                    {
                        if (TeamUtil.IsTeammate(player.EntityId, intNode.BizId))
                        {
                            continue;
                        }
                    }
                    var count = ret.BattleData.GetValueOrCreate(dataType);
                    ret.BattleData[dataType] = count + intNode.Count;
                }
            }
            return ret;
        }

        private static void RecordExplosive(this TerritoryPlunderComponent self, PlayerEntity player, BattleSummary attackerSummary)
        {
            var containerIndex = TerritoryPlunderContainerNodeIndex.UseExplosive;
            var playerContainer = self.SystemRootNode.GetNodeByRelativePath(player.EntityId, containerIndex) as DirectoryNode;
            if (playerContainer == null)
            {
                self.Logger.Info($"UseExplosive container not found. role {player.EntityId}");
                return;
            }
            int sulfurNum = 0;
            int lowQualityFuelNum = 0;
            foreach (var (idx, node) in playerContainer)
            {
                if (node is not IntNode intNode)
                {
                    self.Logger.Error($"UseExplosive node is not int node. {player.RoleId} {node}");
                    continue;
                }
                var explosiveData = McCommon.Tables.TbPlunderExplosive.GetOrDefault(intNode.BizId);
                if (explosiveData == null)
                {
                    self.Logger.Error($"UseExplosive has no config, {intNode.BizId}");
                    continue;
                }

                sulfurNum += (int)explosiveData.SulfurNum * intNode.Count;
                lowQualityFuelNum += (int)explosiveData.LowQualityFuelNum * intNode.Count;
                attackerSummary.UsedExplosiveItems.TryGetValue(intNode.BizId, out var preCount);
                attackerSummary.UsedExplosiveItems[intNode.BizId] = preCount + intNode.Count;
            }
            attackerSummary.BattleData[BattleSummaryDataType.SULFUR_NUM] = sulfurNum;
            attackerSummary.BattleData[BattleSummaryDataType.LOW_QUALITY_FUEL_NUM] = lowQualityFuelNum;
        }

        private static void RecordDestroyedFurnishing(this TerritoryPlunderComponent self, PlayerEntity player, BattleSummary attackerSummary)
        {
            var containerIndex = TerritoryPlunderContainerNodeIndex.DestroyFurnishing;
            var playerContainer = self.SystemRootNode.GetNodeByRelativePath(player.EntityId, containerIndex) as DirectoryNode;
            if (playerContainer == null)
            {
                self.Logger.Info($"DestroyFurnishing container not found. role {player.EntityId} index {containerIndex}");
                return;
            }
            int totalFurnishingCount = 0;
            foreach (var (idx, node) in playerContainer)
            {
                if (node is not IntNode intNode)
                {
                    self.Logger.Error($"DestroyFurnishing node is not int node. {player.RoleId} {node}");
                    continue;
                }
                totalFurnishingCount += intNode.Count;
                attackerSummary.DestroyedFurnishing.TryGetValue(intNode.BizId, out var furnishingCount);
                attackerSummary.DestroyedFurnishing[intNode.BizId] = furnishingCount + intNode.Count;
            }
            attackerSummary.BattleData.TryGetValue(BattleSummaryDataType.DESTROY_PARTS_TIMES, out var existingCount);
            attackerSummary.BattleData[BattleSummaryDataType.DESTROY_PARTS_TIMES] = totalFurnishingCount + existingCount;
        }
        private static void RecordDestroyedConstruction(this TerritoryPlunderComponent self, PlayerEntity player, BattleSummary attackerSummary)
        {
            var containerIndex = TerritoryPlunderContainerNodeIndex.DestroyConstruction;
            var playerContainer = self.SystemRootNode.GetNodeByRelativePath(player.EntityId, containerIndex) as DirectoryNode;
            if (playerContainer == null)
            {
                self.Logger.Info($"DestroyConstruction container not found. role {player.EntityId} index {containerIndex}");
                return;
            }
            int totalConstructionCount = 0;
            foreach (var (idx, node) in playerContainer)
            {
                if (node is not PartNode partNode)
                {
                    self.Logger.Error($"DestroyConstruction node is not int node. {player.RoleId} {node}");
                    continue;
                }
                var addCount = self.AddDestroyedConstruction(attackerSummary, partNode);
                totalConstructionCount += addCount;
            }
            attackerSummary.BattleData.TryGetValue(BattleSummaryDataType.DESTROY_PARTS_TIMES, out var existingCount);
            attackerSummary.BattleData[BattleSummaryDataType.DESTROY_PARTS_TIMES] = totalConstructionCount + existingCount;
        }
        private static int AddDestroyedConstruction(this TerritoryPlunderComponent self, BattleSummary attackerSummary, PartNode partNode)
        {
            int totalCount = 0;
            if (!attackerSummary.DestroyedContruction.TryGetValue(partNode.BizId, out var destroyData))
            {
                destroyData = new PartGradeCount();
                attackerSummary.DestroyedContruction.Add(partNode.BizId, destroyData);
            }

            foreach (var (level, count) in partNode.LevelCount)
            {
                destroyData.GradeCount.TryGetValue(level, out var existingCount);
                destroyData.GradeCount[level] = count;
                totalCount += count;
            }
            return totalCount;
        }

        private static void RecordPlunderItems(this TerritoryPlunderComponent self, PlayerEntity player, BattleSummary attackerSummary, PlunderReport defenderReport, PlunderReport attackerReport)
        {
            var containerIndex = TerritoryPlunderContainerNodeIndex.PlunderContainer;
            var playerContainer = self.SystemRootNode.GetNodeByRelativePath(player.EntityId, containerIndex) as DirectoryNode;
            if (playerContainer == null)
            {
                self.Logger.Info($"RecordPlunderItems container not found. role {player.EntityId} index {containerIndex}");
                return;
            }

            int totalFurnishingCount = 0;
            foreach (var (idx, node) in playerContainer)
            {
                if (node is not IntNode intNode)
                {
                    self.Logger.Error($"RecordPlunderItems node is not int node. {player.RoleId} {node}");
                    continue;
                }
                totalFurnishingCount += intNode.Count;
                self.Logger.Debug($"RecordPlunderItems lost items {intNode.BizId} {intNode.Count}");
                attackerReport.PlunderItems.TryGetValue(intNode.BizId, out var furnishingCount);
                attackerReport.PlunderItems[intNode.BizId] = furnishingCount + intNode.Count;
                if (defenderReport != null)
                {
                    furnishingCount = 0;
                    defenderReport.PlunderItems.TryGetValue(intNode.BizId, out furnishingCount);
                    defenderReport.PlunderItems[intNode.BizId] = furnishingCount + intNode.Count;
                }

            }
            attackerSummary.BattleData.TryGetValue(BattleSummaryDataType.PLUNDER_ITEMS, out var existingCount);
            attackerSummary.BattleData[BattleSummaryDataType.PLUNDER_ITEMS] = totalFurnishingCount + existingCount;
        }
        private static BattleSummary Clone(this BattleSummary self)
        {
            var ret = new BattleSummary();
            foreach (var (dataType, count) in self.BattleData)
            {
                ret.BattleData[dataType] = count;
            }
            foreach (var (id, count) in self.DestroyedFurnishing)
            {
                ret.DestroyedFurnishing[id] = count;
            }
            foreach (var (id, count) in self.UsedExplosiveItems)
            {
                ret.UsedExplosiveItems[id] = count;
            }
            foreach (var (id, gradeCount) in self.DestroyedContruction)
            {
                ret.DestroyedContruction[id] = gradeCount.Clone();
            }
            return ret;
        }

        [Hotfix]
        public static void InitVirtual(this TerritoryPlunderComponent self)
        {
        }

        [Hotfix]
        public static void PostInitVirtual(this TerritoryPlunderComponent self)
        {
            self.SystemRootNode = new TerritoryPlunderRootNode();
            self.rootNode.RegisterSystemRootNode(self.SystemRootNode);
            self.ListenTargetEvent(ProcessEntity.Instance.EntityId, OnBeforeServerStopEventHotfix);
        }



        private static void RegisterEvent(this TerritoryPlunderComponent self, PlayerEntity playerEntity)
        {
            self.ListenTargetEvent(playerEntity.EntityId, OnDamageEventHotfix);
            self.ListenTargetEvent(playerEntity.EntityId, OnDestroyConstructionEventHotfix);
            self.ListenTargetEvent(playerEntity.EntityId, OnAttackPartEventHotfix);
            self.ListenTargetEvent(playerEntity.EntityId, OnPlunderContainerEventHotfix);
            self.ListenTargetEvent(playerEntity.EntityId, OnPlayerDieEventHotfix);
            self.ListenTargetEvent(playerEntity.EntityId, OnPlayerLootingCorpseBoxEventHotfix);
            self.ListenTargetEvent(playerEntity.EntityId, OnPlayerCreateTeamHotfix);
            self.ListenTargetEvent(playerEntity.EntityId, OnPlayerDieHotfix);
            self.ListenTargetEvent(playerEntity.EntityId, OnPlayerDownHotfix);
            self.ListenTargetEvent(playerEntity.EntityId, OnDestroyPartHotfix);
        }

        [Hotfix(EventCallback = true)]
        public static void OnPlayerLootingCorpseBoxEvent(this TerritoryPlunderComponent self, PlayerLootingBoxEvent playerLootingBoxEvent)
        {
            if (EntityManager.Instance.GetEntity(playerLootingBoxEvent.TargetEntityId) is not BoxEntity boxEntity ||
                boxEntity.BelongToTerritoryEntityId != self.ParentEntity.EntityId)
            {
                return;
            }

            if (EntityManager.Instance.GetPlayerEntity(playerLootingBoxEvent.EntityId) is PlayerEntity player)
            {
                self.Logger.Debug($"OnPlayerLootingCorpseBoxEvent lost items {playerLootingBoxEvent.BizId} {playerLootingBoxEvent.Count}");
                self.SystemRootNode.EnsureNode<DirectoryNode, CountDirectoryNode>(player.EntityId,
                    TerritoryPlunderContainerNodeIndex.PlunderContainer).IncrementCount(playerLootingBoxEvent.BizId, playerLootingBoxEvent.Count);
            }
        }

        [Hotfix(EventCallback = true)]
        public static void OnPlunderContainerEvent(this TerritoryPlunderComponent self, LootingStorageChangeEvent plunderContainerEvent)
        {
            // 往容器放东西
            if (plunderContainerEvent.IncrementCount >= 0)
            {
                return;
            }

            var storageEntity = EntityManager.Instance.GetEntity(plunderContainerEvent.EntityId);
            //1 领地掉的盒子
            //2 领地内建筑
            if ((storageEntity is BoxEntity boxEntity && boxEntity.BelongToTerritoryEntityId == self.ParentEntity.EntityId) ||
                (storageEntity is PartEntity partEntity && partEntity.TerritoryId == self.ParentEntity.EntityId))
            {
                var count = -plunderContainerEvent.IncrementCount;
                self.Logger.Debug($"OnPlunderContainerEvent lost items {plunderContainerEvent.BizId} {plunderContainerEvent.IncrementCount}");
                self.SystemRootNode.EnsureNode<DirectoryNode, CountDirectoryNode>(plunderContainerEvent.TargetEntityId,
                    TerritoryPlunderContainerNodeIndex.PlunderContainer).IncrementCount(plunderContainerEvent.BizId, count);
            }
        }

        [Hotfix(EventCallback = true)]
        public static void OnPlayerDieEvent(this TerritoryPlunderComponent self, PlayerDieEvent playerDieEvent)
        {
            if (UserManagerEntity.Instance.GetPlayerEntity(playerDieEvent.RoleId) is PlayerEntity player)
            {
                self.SystemRootNode.EnsureNode<DirectoryNode, CountDirectoryNode>(player.EntityId, TerritoryPlunderContainerNodeIndex.Death).IncrementCount(playerDieEvent.KillerPlayerId, 1);
                if (UserManagerEntity.Instance.GetPlayerEntity(playerDieEvent.KillerRoleId) is PlayerEntity killer)
                {
                    self.SystemRootNode.EnsureNode<DirectoryNode, CountDirectoryNode>(killer.EntityId, TerritoryPlunderContainerNodeIndex.Kill).IncrementCount(player.EntityId, 1);
                }
            }
        }

        [Hotfix(EventCallback = true)]
        public static void OnDamageEvent(this TerritoryPlunderComponent self, DamageEvent damageEvent)
        {
            if (!NEED_COUNT_DAMAGE_SELECT_ID.Contains(damageEvent.BizId))
            {
                return;
            }
            if (EntityManager.Instance.GetEntity(damageEvent.TargetEntityId) is not PlayerEntity targetPlayerEntity)
            {
                return;
            }
            PlayerEntity playerEntity = EntityManager.Instance.GetEntity<PlayerEntity>(damageEvent.EntityId);
            self.SystemRootNode.EnsureNode<DirectoryNode, CountDirectoryNode>(playerEntity.EntityId, TerritoryPlunderContainerNodeIndex.Damage).IncrementCount(damageEvent.TargetEntityId, damageEvent.Count);
            self.SystemRootNode.EnsureNode<DirectoryNode, CountDirectoryNode>(targetPlayerEntity.EntityId, TerritoryPlunderContainerNodeIndex.BeDamage).IncrementCount(damageEvent.EntityId, damageEvent.Count);
        }

        [Hotfix(EventCallback = true)]
        public static void OnDestroyConstructionEvent(this TerritoryPlunderComponent self, DestroyPartEvent destroyPartEvent)
        {
            if (self.ParentId != destroyPartEvent.TerritoryId)
                return;
            if (McCommon.Tables.TbBuildingCore.GetByKey(destroyPartEvent.BizId) is not BuildingCore partConfig)
            {
                return;
            }
            self.Logger.Info($"OnDestroyConstructionEvent BizId {destroyPartEvent.BizId} Grade {destroyPartEvent.Grade}");
            if (partConfig.Type == (int)ConstructionPartType.Core)
            {
                PlayerEntity playerEntity = EntityManager.Instance.GetEntity<PlayerEntity>(destroyPartEvent.EntityId);
                if (self.SystemRootNode.GetChildNode(playerEntity.EntityId) is not DirectoryNode playerDirectoryNode)
                {
                    playerDirectoryNode = new DirectoryNode(playerEntity.EntityId, playerEntity.EntityId);
                    self.SystemRootNode.AddChildNode(playerDirectoryNode);
                }
                if (playerDirectoryNode.GetChildNode(TerritoryPlunderContainerNodeIndex.DestroyConstruction) is not DirectoryNode constructionContainer)
                {
                    constructionContainer = new DirectoryNode(TerritoryPlunderContainerNodeIndex.DestroyConstruction, TerritoryPlunderContainerNodeIndex.DestroyConstruction);
                    playerDirectoryNode.AddChildNode(constructionContainer);
                }

                if (constructionContainer.GetChildNode(destroyPartEvent.BizId) is not PartNode constructionNode)
                {
                    constructionNode = new PartNode(destroyPartEvent.BizId, destroyPartEvent.BizId, 1,
                        destroyPartEvent.Grade);
                    constructionContainer.AddChildNode(constructionNode);
                }
                else
                {
                    constructionNode.LevelCount.TryGetValue(destroyPartEvent.Grade, out var preCount);
                    constructionNode.LevelCount[destroyPartEvent.Grade] = preCount + 1;
                }
            }
            else
            {
                PlayerEntity playerEntity = EntityManager.Instance.GetEntity<PlayerEntity>(destroyPartEvent.EntityId);

                self.SystemRootNode.EnsureNode<DirectoryNode, CountDirectoryNode>(playerEntity.EntityId,
                    TerritoryPlunderContainerNodeIndex.DestroyFurnishing).IncrementCount(destroyPartEvent.BizId, 1);
            }
        }

        [Hotfix(EventCallback = true)]
        public static void OnAttackPartEvent(this TerritoryPlunderComponent self, AttackPartEvent attackPartEvent)
        {
            if (!self.IsExplosive(attackPartEvent.DirectItemId))
            {
                return;
            }

            PlayerEntity playerEntity = EntityManager.Instance.GetEntity<PlayerEntity>(attackPartEvent.SourceEntityId);

            // 通过 SourceClientTime 筛选重复的攻击事件
            // 玩家同一时刻只能发起一次攻击，因此key只需要使用玩家id即可
            if (self.lastPlayerAttackTime.TryGetValue(playerEntity.EntityId, out var attackedTimeSet) &&
                attackedTimeSet.ContainsWithoutLinq(attackPartEvent.SourceClientTime))
            {
                return;
            }

            if (!self.lastPlayerAttackTime.TryGetValue(playerEntity.EntityId, out _))
            {
                attackedTimeSet = new HashSet<long>();
                self.lastPlayerAttackTime.Add(playerEntity.EntityId, attackedTimeSet);
            }
            self.lastPlayerAttackTime[playerEntity.EntityId].Add(attackPartEvent.SourceClientTime);

            self.SystemRootNode
                .EnsureNode<DirectoryNode, CountDirectoryNode>(playerEntity.EntityId,
                    TerritoryPlunderContainerNodeIndex.UseExplosive).IncrementCount(attackPartEvent.DirectItemId, 1);
        }

        private static bool IsExplosive(this TerritoryPlunderComponent self, long bizId)
        {
            return McCommon.Tables.TbPlunderExplosive.DataMap.ContainsKey(bizId);
        }
    }
}

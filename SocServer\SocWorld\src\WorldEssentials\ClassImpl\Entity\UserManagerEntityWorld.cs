using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.SocWorld.Lobby;
using WizardGames.Soc.SocWorld.Network;

namespace WizardGames.Soc.Share.Framework
{
    public partial class UserManagerEntity
    {
        public static UserManagerEntity Instance { get; private set; } = new(FrameworkEntityConstId.USER_MANAGER_ENTITY_ID);

        private UserManagerEntity(long id) : base(id)
        {
            if (Instance != null) throw new System.Exception("Duplicate create UserManagerEntity");
            Instance = this;
        }

        public ConcurrentDictionary<ulong, Client> RoleId2Client { get; } = new ConcurrentDictionary<ulong, Client>(3, 500);

        public ConcurrentDictionary<int, Client> NetPeerId2Client { get; } = new ConcurrentDictionary<int, Client>(3, 500);

        public ConcurrentDictionary<long, int> EntityId2ClientNetPeerId { get; } = new ConcurrentDictionary<long, int>(3, 500);

        private Dictionary<int, ulong> connectingNetPeers = new();

        internal Dictionary<ulong, long> roleId2PlayerEntityId { get; } = new(500);
        public readonly HashSet<ulong> LoadingSuccessRoleIds = new(200);
        public IEnumerable<long> PlayerEntityIds => roleId2PlayerEntityId.Values;
        public IEnumerable<ulong> TotalRoleIds => roleId2PlayerEntityId.Keys;

        public IEnumerable<ulong> ConnectingRoleIds => connectingNetPeers.Values;

        public int ConnectedPlayerCount => RoleId2Client.Count;

        /// <summary>
        /// 服务器人数总和 不包括占位玩家
        /// </summary>
        public int TotalPlayerCount => roleId2PlayerEntityId.Count;

        /// <summary>
        /// 服务器人数总和 包括占位玩家
        /// </summary>
        public int TotalPlayerCountWithHolder => roleId2PlayerEntityId.Count + ServerInstanceEntity.Instance.PlaceHolderPlayer?.Count ?? 0;
        public HashSet<ulong> OnlineInLobbyRoleIds = new();
        internal Dictionary<ulong, long> temporaryRoleId2EntityId = new();
        public IEnumerable<ulong> TemporaryRoleIds => temporaryRoleId2EntityId.Keys;
        internal long kickAllTimerId = TimerWheelConst.INVALID_TIMER_ID;

        // 检测服务器是否变成鬼服
        private int maxOnlinePlayerCountIn24Hours;
        private const int GHOST_PLAYER_COUNT_THRESHOLD = 10;

        public override void Init()
        {
            // 这段代码暂时屏蔽掉，该版本不需要有鬼服状态 // todo@bxl: 下个版本加回来
            //var gameState = ProcessEntity.Instance.archive.GameState;
            //if (gameState != ProcessEntity.GS_GHOST && gameState != ProcessEntity.GS_READY_TO_STOP)
            //{
            //    BeginCheckGhost();
            //}
        }

        public void AddConnectingNetPeer(int netPeerId, ulong roleId)
        {
            connectingNetPeers.Add(netPeerId, roleId);
        }

        public bool HasConnectingPeer(int netPeerId) => connectingNetPeers.ContainsKey(netPeerId);

        public bool HasConnectedPeer(int netPeerId) => NetPeerId2Client.ContainsKey(netPeerId);

        public void RemoveConnectingPeer(int netPeerId)
        {
            connectingNetPeers.Remove(netPeerId, out var roleId);
        }

        public void AddClient(Client client)
        {
            if (client == null)
                return;

            RoleId2Client[client.RoleId] = client;
            NetPeerId2Client[client.NetPeerId] = client;

            // todo@bxl: 下个版本加回来
            //var count = RoleId2Client.Count;
            //if (count > maxOnlinePlayerCountIn24Hours)
            //{
            //    maxOnlinePlayerCountIn24Hours = count;
            //    logger.Info($"New max online player count in 24 hours: {maxOnlinePlayerCountIn24Hours}");
            //}
        }

        public bool RemoveClient(int netPeerId, out Client client)
        {
            client = GetUserByNetPeerId(netPeerId);
            if (client == null)
                return false;

            RoleId2Client.Remove(client.RoleId, out Client _);
            NetPeerId2Client.Remove(netPeerId, out Client _);
            LoadingSuccessRoleIds.Remove(client.RoleId);
            return true;
        }

        public void SetEntityNetPeerId(long entityId, int netPeerId)
        {
            EntityId2ClientNetPeerId[entityId] = netPeerId;
        }

        public void RemoveEntityNetPeerId(long entityId)
        {
            if (!EntityId2ClientNetPeerId.TryRemove(entityId, out var _))
            {
                logger.Error($"RemoveEntityClient {entityId} netpeerId not found.");
            }
        }

        [ThreadSafe]
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public bool GetNetPeerIdByEntityId(long entityId, out int netPeerId)
        {
            return EntityId2ClientNetPeerId.TryGetValue(entityId, out netPeerId);
        }

        public void SetClientLoadingSuccess(ulong roleId)
        {
            if (!RoleId2Client.TryGetValue(roleId, out var client))
            {
                logger.Warn($"SetPlayerOnline roleId not found {roleId}");
                return;
            }
            if (client.RoleId != roleId)
            {
                logger.Error($"SetPlayerOnline roleId error. {roleId} {client.RoleId}");
            }
            if (LoadingSuccessRoleIds.Contains(client.RoleId))
            {
                logger.Error($"SetPlayerOnline roleId already online {roleId}");
                return;
            }
            LoadingSuccessRoleIds.Add(client.RoleId);
            LobbyServiceManager.Instance.ReportWorldInfo();
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        [ThreadSafe]
        public bool IsClientOnline(int netPeerId) => NetPeerId2Client.ContainsKey(netPeerId);

        [ThreadSafe]
        public Client GetClientByRoleId(ulong roleId)
        {
            if (RoleId2Client.TryGetValue(roleId, out var user))
            {
                return user;
            }
            return null;
        }


        [ThreadSafe]
        public int GetNetPeerIdByRoleId(ulong roleId)
        {
            if (RoleId2Client.TryGetValue(roleId, out var user))
            {
                return user.NetPeerId;
            }
            return -1;
        }

        [ThreadSafe]
        public Client GetUserByRoleId(ulong roleId)
        {
            if (RoleId2Client.TryGetValue(roleId, out var user)) return user;
            return null;
        }

        [ThreadSafe]
        public Client GetUserByNetPeerId(int netPeerId)
        {
            if (NetPeerId2Client.TryGetValue(netPeerId, out var user)) return user;
            return null;
        }

        [ThreadSafe]
        public ulong GetRoleIdByNetPeerId(int netPeerId)
        {
            if (NetPeerId2Client.TryGetValue(netPeerId, out var user))
            {
                return user.RoleId;
            }
            return 0;
        }

        public void AddPlayerEntity(ulong roleId, long entityId)
        {
            roleId2PlayerEntityId[roleId] = entityId;
        }

        /// <summary>
        /// 废弃
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public PlayerEntity GetPlayerEntityOnline(ulong roleId)
        {
            var user = GetUserByRoleId(roleId);
            if (user == null) return null;
            return GetPlayerEntity(user.RoleId);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public PlayerEntity GetPlayerEntityByNetPeerId(int netPeerId)
        {
            var roleId = GetRoleIdByNetPeerId(netPeerId);
            if (roleId == 0) return null;
            return GetPlayerEntity(roleId);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public PlayerEntity GetPlayerEntity(ulong roleId)
        {
            if (roleId2PlayerEntityId.TryGetValue(roleId, out var entityId))
            {
                var entity = EntityManager.Instance.GetEntity(entityId) as PlayerEntity;
                return entity;
            }

            return null;
        }

        public PlayerEntity DebugGetOnePlayer()
        {
            return EntityManager.Instance.GetEntity(roleId2PlayerEntityId.First().Value) as PlayerEntity;
        }


        public bool IsClientLoadingSuccess(ulong roleId) => LoadingSuccessRoleIds.Contains(roleId);

        public bool IsPlayerOverServerLimit()
        {
            // 判断服务器人数是否已满
            var playRuleConfig = McCommon.Tables.TBPlayModeMain.GetOrDefault(ServerInstanceEntity.Instance.GameModeId);
            // 对私服做兼容
            var limit = playRuleConfig == null ? 10000 : playRuleConfig.TotalUserLimit;
            return TotalPlayerCountWithHolder >= limit;
        }

        public void BeginCheckGhost()
        {
            EntityTimerWheel.AddTimerRepeat(24 * 60 * 60 * 1000, 24 * 60 * 60 * 1000, (timerId, _, _) =>
            {
                if (maxOnlinePlayerCountIn24Hours < GHOST_PLAYER_COUNT_THRESHOLD)
                {
                    var gameState = ProcessEntity.Instance.archive.GameState;
                    if (gameState != ProcessEntity.GS_GHOST && gameState != ProcessEntity.GS_READY_TO_STOP)
                    {
                        ProcessEntity.Instance.SetGameState(ProcessEntity.GS_GHOST);
                        ProcessEntity.Instance.WriteStatusToFile(ProcessEntity.GS_GHOST);
                        logger.Info($"Server has become ghost server due to low player count: {maxOnlinePlayerCountIn24Hours} < {GHOST_PLAYER_COUNT_THRESHOLD}");
                    }
                    maxOnlinePlayerCountIn24Hours = 0; // 重置计数
                }
            });
        }

        #region 玩家统计

        public void SetUserCmdRecvTime(ulong roleId, long timestamp)
        {
            if (RoleId2Client.TryGetValue(roleId, out var data))
            {
                data.LastUserCmdTimestamp = timestamp;
            }
            else
            {
                // 断线后可能还会收到cmd
                logger.Warn($"receive user cmd suc but role data not found. {roleId}");
            }
        }

        public void CalcIndicatorData(out int waitLoadingSucNum, out int waitFirstUserCmdNum, out int userCmdNormalNum, out int userCmdInterruptNum)
        {
            waitLoadingSucNum = waitFirstUserCmdNum = userCmdNormalNum = userCmdInterruptNum = 0;
            var now = TimeStampUtil.GetNowTimeStampMSec();
            foreach (var (roleId, data) in RoleId2Client)
            {
                if (!IsClientLoadingSuccess(roleId))
                {
                    waitLoadingSucNum += 1;
                }
                else
                {
                    if (data.LastUserCmdTimestamp == 0)
                    {
                        waitFirstUserCmdNum += 1;
                    }
                    else if (now - data.LastUserCmdTimestamp < 5000)
                    {
                        userCmdNormalNum += 1;
                    }
                    else
                    {
                        userCmdInterruptNum += 1;
                    }
                }
            }
        }

        public void AddTemporaryEntityWithClient(EntityWithClient entity)
        {
            temporaryRoleId2EntityId[entity.GetRoleId()] = entity.EntityId;
        }

        public T GetTemporaryEntityWithClient<T>(ulong roleId) where T : EntityWithClient
        {
            if (temporaryRoleId2EntityId.TryGetValue(roleId, out var entityId))
            {
                return EntityManager.Instance.GetEntity(entityId) as T;
            }
            return null;
        }

        public T GetTemporaryEntityWithClientByNetPeerId<T>(int netPeerId) where T : EntityWithClient
        {
            if (NetPeerId2Client.TryGetValue(netPeerId, out var c))
            {
                if (temporaryRoleId2EntityId.TryGetValue(c.RoleId, out var entityId))
                {
                    return EntityManager.Instance.GetEntity(entityId) as T;
                }
            }
            return null;
        }

        public bool TryRemoveTemporaryEntityWithClientAndDestroyEntity(ulong roleId, ENetPeerIdSetReason reason)
        {
            if (temporaryRoleId2EntityId.TryGetValue(roleId, out var entityId))
            {
                var ent = EntityManager.Instance.GetEntity(entityId) as EntityWithClient;
                ent?.SetNetPeerId(NetConst.INVALID_NET_PEER_ID, reason);
                EntityManager.Instance.RemoveEntity(entityId);
                temporaryRoleId2EntityId.Remove(roleId);
                return true;
            }
            return false;
        }
        #endregion
    }
}
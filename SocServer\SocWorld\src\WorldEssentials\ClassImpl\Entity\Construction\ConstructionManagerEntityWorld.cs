using System.Collections.Generic;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Framework.Algorithm;
using WizardGames.Soc.Common.Framework.Algorithm.Octree;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Construction;

namespace WizardGames.Soc.Common.Entity
{
    public struct PartRemoveParam    
    {
        public static PartRemoveParam Default = new();

        public PartRemoveParam() { }
        public PartRemoveParam(EDestroyPartReason removeReason, EStabilityCheckReason stabilityReason, long sourceId, long weaponTableId) 
        {
            RemoveReason = removeReason;
            StabilityReason = stabilityReason;
            SourceId = sourceId;
            WeaponTableId = weaponTableId;
        }
        public EDestroyPartReason RemoveReason = EDestroyPartReason.Normal;
        public EStabilityCheckReason StabilityReason = EStabilityCheckReason.None;
        public long SourceId;
        public long WeaponTableId;
        public void Reset()
        {
            RemoveReason = EDestroyPartReason.Normal;
            StabilityReason = EStabilityCheckReason.None;
            SourceId = 0;
            WeaponTableId = 0;
        }
        public override string ToString()
        {
            return $"[{RemoveReason}-{StabilityReason}-{SourceId}-{WeaponTableId}]";
        }
    }

    public class CurrentPartOpInfo
    {
        public ulong RPCRoleId;
        public PartRemoveParam RemoveParam;
        public WaitSimulatorCheckDataBase WaitData;
        public CustomPartGroupData PartGroupData;

        public void Reset()
        {
            RPCRoleId = 0;
            RemoveParam.Reset();
            WaitData = null;
            PartGroupData = null;
        }
    }

    public partial class ConstructionManagerEntity
    {
        public static ConstructionManagerEntity Instance { get; internal set; }
        public static ConstructionManagerEntity Create()
        {
            if (Instance != null) throw new System.Exception("Duplicate create ConstructionManagerEntity");
            return new(EntityConstId.CONSTRUCTION_MANAGER_ENTITY_ID);
        }

        public readonly CurrentPartOpInfo CurrentPartOpInfo = new();

        #region 区域锁
        internal class LockRecord
        {
            public long TimerId;
            public Bounds CheckBounds;
            public int LockCount;
        }

        /// <summary>
        /// 每次循环最大移除建筑数目
        /// </summary>
        public const int MAX_REMOVE_COUNT_PER_TICK = 5;
        internal long batchRecoverTimerId = TimerWheelConst.INVALID_TIMER_ID;

        // 区域锁：锁定一块区域，一段时间内只能有一个建造行为
        internal BoundsOctree<long> regionLock;
        internal Dictionary<long, LockRecord> curLocks;
        internal int regionLockCount;

        internal const uint MAX_REGION_LOCK_SEC = 10;

        internal List<long> collidingLockCache = new();
        #endregion


        #region 建造逻辑相关
        /// <summary>
        /// 是否有死羊正在载入，实现有问题，如果刷死羊的同时创建状态就会不对
        /// </summary>
        public bool IsLoadingPartGroup
        {
            get => PartGroupQueue.Count > 0;
        }
        public bool IsGmRemovingParts
        {
            get
            {
                return RemoveQueue.Count > 0;
            }
        }
        internal ElectricManagerEntity mgrElectric => ElectricManagerEntity.Instance;
        /// <summary>
        /// 是否允许无限建造
        /// </summary>
        internal readonly bool acceptNoConsume = true;

        /// <summary>
        /// 缓存建筑删除参数，在remove会调里用到
        /// 一般情况只有一个；如果在entity的remove会调里递归删除建筑，则会有多个建筑删除参数
        /// 参数缓存和参数使用应该在一个同步逻辑内完成
        /// </summary>
        internal Dictionary<long, PartRemoveParam> partRemoveParams = new(4);
        /// <summary>
        /// 蓝图分析队列数据
        /// </summary>
        public class AnalysisBlueprintQueueData
        {
            public long id;
            public Queue<long> partIdQueue;
        }
        //蓝图分析队列
        public Queue<AnalysisBlueprintQueueData> AnalysisBlueprintQueue { get; } = new();

        ///每次查询某区域内建筑的列表
        internal List<long> checkConstructionList = new();

        internal Dictionary<long, WaitSimulatorCheckBlueprintCreateData> waitSimulatorCheckBlueprintCreateData = new();
      //  internal Dictionary<long, WaitSimulatorCheckData> waitSimulatorCheckDatas = new();
        //temp wait simulator data
        internal Dictionary<long, WaitSimulatorCheckDataBase> waitSimulatorCheckDict = new();
        internal DecayUnit decayUnit = new DecayUnit();

        internal List<long> removedParts = new();

        internal OrderedDictionary<long, DirtyStabilityConstructionData> surroundingConstructionDirtyDict = new();
        internal OrderedDictionary<long, DirtyStabilityConstructionData> stabilityCheckDict = new();
        internal Queue<DirtyStabilityBoundsData> surroundingBoundsDirtyQueue = new();

        internal List<long> lightCheckList = new();

        /// <summary>
        /// 记录每一名玩家的上次建造请求信息，包括建筑类型和请求时间
        /// </summary>
        internal Dictionary<long, Dictionary<long, CreatePartRecordInfo>> playerCreatePartRequestRecord = new();

        #endregion

        private ConstructionManagerEntity(long id) : base(id)
        {
            PartGroupQueue = new();
            RemoveQueue = new();
            RecoverQueue = new();
        }

        [HotfixableVirtual]
        public override partial void PostInit(bool isLoadFromDb);
    }
}

using System.Collections.Generic;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;

namespace WizardGames.Soc.Common.Entity
{
    public static partial class ConstructionManagerEntityHotfix
    {
        public static void EnqueuePartGroupData(this ConstructionManagerEntity self, CustomPartGroupData partGroupData)
        {
            self.PartGroupQueue.Enqueue(partGroupData);
        }

        public static CustomPartGroupData DequeuePartGroupData(this ConstructionManagerEntity self)
        {
            var partGroupData = self.PartGroupQueue.Dequeue<CustomPartGroupData>();
            return partGroupData;
        }

        public static bool CheckPartInLoading(this ConstructionManagerEntity self, long entityId)
        {
            return self.GetCurrentPartLoadingDeadSheepData(entityId) == null;
        }

        public static bool NeedTakePhoto(this ConstructionManagerEntity self, long entityId)
        {
            // 存档读取流程中创建的建筑不需要拍照
            if (ServerControlEntity.Instance.InLoadingFromDbProgress) { return false; }

            // 死羊载入流程
            var curLoadingData = self.CurrentPartOpInfo.PartGroupData;
            if (curLoadingData == null)
            {
                return true;
            }
            //self.Logger.Error($"curLoadingData.CreatePartReason {curLoadingData.CreatePartReason}");
            //CreatePartReason.CreateByPlayerBlueprint: 玩家蓝图流程不在死羊加载这里，所以判定不到，目前也没有前置检查所以必须拍照
            //CreatePartReason.CreateByGraphNode: 后续区分是不是UGC，没法区分并且没有前置检查的话就必须拍
            switch (curLoadingData.CreatePartReason)
            {
                case (int)CreatePartReason.CreateFromGMLoadPartGroup:
                case (int)CreatePartReason.CreateByGraphNode:
                    return true;
                case (int)CreatePartReason.CreateFromDeadSheep:
                case (int)CreatePartReason.CreateFromConstructionBlueprint:
                case (int)CreatePartReason.CreateByTask:
                    return false;
                default:
                    return true;
            }
        }

        public static CustomPartGroupData GetCurrentPartLoadingDeadSheepData(this ConstructionManagerEntity self, long entityId)
        {
            if (self.PartGroupQueue.Count == 0)
                return null;
            var curRecoverPartGroupData = self.PartGroupQueue.Peek<CustomPartGroupData>();
            if (curRecoverPartGroupData != null && curRecoverPartGroupData.RecoverPartIds.Contains(entityId))
            {
                return curRecoverPartGroupData;
            }
            return null;
        }

        public static CustomVector3 GetCurrentLoadingCenter(this ConstructionManagerEntity self)
        {
            if (self.PartGroupQueue.Count == 0)
                return null;
            var curRecoverPartGroupData = self.PartGroupQueue.Peek<CustomPartGroupData>();
            return curRecoverPartGroupData.CenterPos;
        }

        public static long GetCurrentLoadingTerritory(this ConstructionManagerEntity self)
        {
            if (self.PartGroupQueue.Count == 0)
                return 0;
            var curRecoverPartGroupData = self.PartGroupQueue.Peek<CustomPartGroupData>();
            return curRecoverPartGroupData.CreatedTerritoryId;
        }
        public static void EnqueueRemovePartList(this ConstructionManagerEntity self, IEnumerable<long> partList, RemovePartRecord record, EDestroyPartReason destroyReason)
        {
            var removeData = new RemovePartData(partList, record, (int)destroyReason);
            self.RemoveQueue.Enqueue(removeData);
        }
    }
}

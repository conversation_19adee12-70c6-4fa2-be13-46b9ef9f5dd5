using System.Buffers;
using System.Collections.Generic;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocWorld;

namespace WizardGames.Soc.Common.Entity
{
    [HotfixClass]
    public static partial class TeamManagerEntityHotfix
    {
        // 每日凌晨5点 给队长挂定时器 定时器时间内都没有上线 就触发弹劾
        private const int CHECK_IMPEACH_HOUR = 5;
        [Hotfix]
        public static void Init(this TeamManagerEntity self)
        {
            self.WarZoneTeamRecruitments = new();
            self.AllTeamId = new();
        }

        [Hotfix]
        public static void PostInit(this TeamManagerEntity self, bool isLoadFromDb)
        {
            TeamManagerEntity.Instance = self;

            self.teamRecruitmentId2CreatedTime = new();
            foreach (var teamId in self.WarZoneTeamRecruitments.Keys)
            {
                self.teamRecruitmentId2CreatedTime.Update(teamId, TimeStampUtil.GetNowSec());
            }

            self.AddTimerRepeat(60 * 1000, 60 * 1000, OnCheckTeamRecruitmentsHotfix);

            if (!isLoadFromDb)
            {
                // 这里是否补偿呢 有点纠结
                self.AddSchedule(CHECK_IMPEACH_HOUR, 0, false, CheckTeamTimerHotfix);
            }
        }

        [Hotfix(TimerCallback = true)]
        private static void OnCheckTeamRecruitments(this TeamManagerEntity self, long _)
        {
            foreach (var (teamId, recruitment) in self.WarZoneTeamRecruitments)
            {
                if (RecruitmentIsInvalid(recruitment))
                {
                    self.RemoveRecruitment(teamId);
                }
            }
        }

        // 添加一个新招募，会覆盖旧的
        public static void AddRecruitment(this TeamManagerEntity self, WarZoneTeamRecruitment recruitment)
        {
            recruitment.DisableTs = ProcessEntity.Instance.SecSinceStartup + McCommon.Tables.TbGlobalConfig.RecruitmentExpirationTime * 60 * 60;
            self.WarZoneTeamRecruitments[recruitment.TeamId] = recruitment;
            self.teamRecruitmentId2CreatedTime.Update(recruitment.TeamId, TimeStampUtil.GetNowSec());
        }

        // 删除招募
        public static bool RemoveRecruitment(this TeamManagerEntity self, long teamId)
        {
            if (!self.WarZoneTeamRecruitments.ContainsKey(teamId))
            {
                return false; // 招募不存在
            }

            var team = EntityManager.Instance.GetEntity(teamId) as TeamEntity;
            if (team != null)
            {
                foreach (var memberEntity in team.GetPlayerMember())
                {
                    memberEntity.ComponentTeam.RemoteCallSelfTeamRecruitNotify(ERpcTarget.OwnClient, true, ReadOnlySequence<byte>.Empty);
                }
            }

            self.teamRecruitmentId2CreatedTime.Remove(teamId);
            self.WarZoneTeamRecruitments.Remove(teamId);
            return true;
        }

        // 获取招募
        public static WarZoneTeamRecruitment GetRecruitment(this TeamManagerEntity self, long teamId)
        {
            if (self.WarZoneTeamRecruitments.TryGetValue(teamId, out var recruitment))
            {
                if (RecruitmentIsInvalid(recruitment))
                {
                    self.RemoveRecruitment(teamId);
                    return null; // 招募已失效
                }
                else
                {
                    return recruitment;
                }
            }
            return null;
        }

        // 获取条件匹配的招募
        public static void GetRecruitmentByCondition(this TeamManagerEntity self, List<long> teamIds, List<int> teamPreferences, bool microphone)
        {
            // 遍历所有招募 按匹配度生成列表 
            // 匹配度 --》 招募队伍Id
            Dictionary<int, List<long>> matchedTeamRecruitments = [];
            foreach (var recruitment in self.WarZoneTeamRecruitments.Values)
            {
                if (RecruitmentIsInvalid(recruitment))
                {
                    self.RemoveRecruitment(recruitment.TeamId);
                }

                if (recruitment.Microphone != microphone)
                {
                    continue; // 不匹配
                }

                var matchingDegree = 0; // 匹配度
                if (teamPreferences.Count != 0)
                {
                    foreach (var teamPreference in teamPreferences)
                    {
                        if (recruitment.TeamPreferences.Contains(teamPreference))
                        {
                            matchingDegree++;
                        }
                    }
                }
                else
                {
                    // 如果没有偏好职业，则匹配度+1
                    matchingDegree++;
                }

                if (matchingDegree == 0)
                {
                    continue; // 不匹配
                }

                if (!matchedTeamRecruitments.TryGetValue(matchingDegree, out var recruitmentIds))
                {
                    matchedTeamRecruitments.Add(matchingDegree, recruitmentIds = []);
                }
                recruitmentIds.Add(recruitment.TeamId);
            }

            var maxMatchingDegree = teamPreferences.Count == 0 ? 1 : teamPreferences.Count;
            for (int i = maxMatchingDegree; i >= 0; i--)
            {
                if (matchedTeamRecruitments.TryGetValue(i, out var recruitmentIds))
                {
                    foreach (var teamId in recruitmentIds)
                    {
                        teamIds.Add(teamId);
                        if (teamIds.Count >= McCommon.Tables.TBConsts.RecruitmentListNumber)
                        {
                            break;
                        }
                    }
                    if (teamIds.Count >= McCommon.Tables.TBConsts.RecruitmentListNumber)
                    {
                        break;
                    }
                }
            }

            self.Logger.Info($"GetRecruitmentByCondition: {string.Join(" ", teamIds)}");
        }

        // 检查招募是否失效
        public static bool RecruitmentIsInvalid(WarZoneTeamRecruitment recruitment)
        {
            if (ProcessEntity.Instance.SecSinceStartup >= recruitment.DisableTs)
            {
                return true;
            }

            var team = EntityManager.Instance.GetEntity(recruitment.TeamId) as TeamEntity;
            if (team == null)
            {
                return true;
            }

            if (team.CaptainRoleId != GetCaptainRoleIdByRecruitment(recruitment))
            {
                return true; // 队长不匹配
            }

            return false;
        }

        // 获取招募信息里的队长roleId
        public static ulong GetCaptainRoleIdByRecruitment(WarZoneTeamRecruitment recruitment)
        {
            foreach (var (roleId, teamMember) in recruitment.TeamMembers)
            {
                if (teamMember.IsCaptain)
                {
                    return roleId;
                }
            }
            return 0;
        }

        // 获取某个时间点后所以招募简要信息
        public static void GetTeamRecruitmentBriefList(this TeamManagerEntity self, long excludeTeamId, int startTime, CustomTypeList<WarZoneTeamRecruitmentBriefInfo> recruitmentBriefInfos)
        {
            foreach (var (teamId, createdTime) in self.teamRecruitmentId2CreatedTime.GetReversedByValue())
            {
                if (teamId == excludeTeamId)
                {
                    continue;
                }

                if (createdTime >= startTime)
                {
                    var recruitment = self.WarZoneTeamRecruitments[teamId];
                    if (RecruitmentIsInvalid(recruitment))
                    {
                        continue;
                    }
                    else
                    {
                        var info = new WarZoneTeamRecruitmentBriefInfo
                        {
                            TeamId = teamId,
                            CaptainRoleId = GetCaptainRoleIdByRecruitment(recruitment),
                            MemberNum = recruitment.TeamMembers.Count,
                        };
                        var captain = UserManagerEntity.Instance.GetPlayerEntity(info.CaptainRoleId);
                        if (captain != null)
                        {
                            info.CaptainPortrait = captain.Portrait;
                            info.CaptainName = captain.Name;
                        }
                        recruitmentBriefInfos.Add(info);

                        if (recruitmentBriefInfos.Count >= McCommon.Tables.TbGlobalConfig.MaximumNumberOfRecruitment)
                        {
                            break;
                        }
                    }
                }
                else
                {
                    break;
                }
            }
        }

        [Hotfix(ScheduleCallback = true)]
        private static void CheckTeamTimer(this TeamManagerEntity self, long _)
        {
            foreach (var teamId in self.AllTeamId)
            {
                var teamEntity = EntityManager.Instance.GetEntity(teamId) as TeamEntity;
                if (teamEntity == null) continue;
                // 队伍人数小于2 不需要弹劾 也不需要转移领地权限
                if (teamEntity.MemberDic.Count < 2) continue;
                var captainRoleId = teamEntity.CaptainRoleId;
                var captain = UserManagerEntity.Instance.GetPlayerEntity(captainRoleId);
                // 添加检测队长长时间不上线会发生弹劾的定时器
                if (captain != null && captain.CurrentNetPeerId == NetConst.INVALID_NET_PEER_ID && teamEntity.ImpeachTimerId == TimerWheelConst.INVALID_TIMER_ID)
                {
                    teamEntity.AddCheckCaptainImpeachTimer();
                }

                var timeout = (uint)McCommon.Tables.TbGlobalConfig.TerritoryTransferTime * 60 * 60 * 1000;
                foreach (var (roleId, info) in teamEntity.MemberDic)
                {
                    if (roleId == teamEntity.CaptainRoleId) continue;
                    if (info.CheckTerritoryTimerId != TimerWheelConst.INVALID_TIMER_ID) continue;
                    info.CheckTerritoryTimerId = teamEntity.AddArchiveTimerOnce(timeout, TeamEntityHotfix.CheckTerritoryTimeoutHotfix, new TimerULongParam(roleId));
                }
            }
        }
    }
}

using System;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.SocWorld;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.Test
{
    /// <summary>
    /// 任务系统测试用例
    /// </summary>
    internal class TaskTest : TestCaseBase
    {
        private PlayerEntity player;
        private PlayerTaskComponent taskComponent;

        public override void Run(ArraySegment<string> args)
        {
            InitTable();
            SetupPlayer();

            // 测试任务6150301的计数功能
            TestTask6150301Count();
        }

        /// <summary>
        /// 初始化玩家和任务组件
        /// </summary>
        private void SetupPlayer()
        {
            player = CreatePlayerWithInventory();
            taskComponent = new PlayerTaskComponent();
            player.AddComponent(taskComponent);
            AddEntityOnlyInitComponents(player);

            Print("Player and TaskComponent initialized");
        }

        /// <summary>
        /// 测试任务6150301的计数功能
        /// </summary>
        private void TestTask6150301Count()
        {
            Print("=== Testing Task 6150301 Count Functionality ===");

            const long taskId = 6150301;

            // 检查任务配置是否存在
            var taskConfig = McCommon.Tables.TbQuestPhase.GetOrDefault(taskId);
            if (taskConfig == null)
            {
                Print($"Task config not found for task {taskId}");
                return;
            }

            Print($"Task {taskId} config found: {taskConfig.TaskPhaseDescribe?.Text ?? "No description"}");

            // 创建任务节点
            var taskContainer = GetOrCreateTaskContainer(taskConfig.Type);
            var taskNode = CreateTaskNode(taskContainer, taskId);

            // 创建子任务节点
            var subTaskNode = CreateSubTaskNode(taskNode, taskId);

            // 测试初始计数
            TestInitialCount(taskNode, subTaskNode);

            // 测试计数更新
            TestCountUpdate(taskNode, subTaskNode);

            // 测试计数累加
            TestCountAccumulation(taskNode, subTaskNode);

            // 测试任务完成状态
            TestTaskCompletion(taskNode, subTaskNode, taskConfig);

            Print("=== Task 6150301 Count Test Completed ===");
        }

        /// <summary>
        /// 获取或创建任务容器
        /// </summary>
        private TaskContainer GetOrCreateTaskContainer(int taskType)
        {
            var taskContainerType = (MainTaskType)taskType;
            var containerIndex = GetTaskContainerIndex(taskContainerType);

            var taskContainer = player.Root.GetChildNode(containerIndex) as TaskContainer;
            if (taskContainer == null)
            {
                taskContainer = CreateTaskContainer(taskContainerType, containerIndex);
                player.Root.AddChildNode(taskContainer);
            }

            return taskContainer;
        }

        /// <summary>
        /// 获取任务容器索引
        /// </summary>
        private long GetTaskContainerIndex(MainTaskType taskType)
        {
            return taskType switch
            {
                MainTaskType.GuideTask => PlayerTaskContainerIndex.Guide,
                MainTaskType.DailyTask => PlayerTaskContainerIndex.Daily,
                MainTaskType.PoiTask => PlayerTaskContainerIndex.Poi,
                MainTaskType.MedalTask => PlayerTaskContainerIndex.Medal,
                _ => PlayerTaskContainerIndex.Guide
            };
        }

        /// <summary>
        /// 创建任务容器
        /// </summary>
        private TaskContainer CreateTaskContainer(MainTaskType taskType, long containerIndex)
        {
            return taskType switch
            {
                MainTaskType.GuideTask => new GuideTaskContainer(),
                MainTaskType.DailyTask => new DailyTaskContainer(),
                MainTaskType.PoiTask => new PoiTaskContainer(),
                MainTaskType.MedalTask => new MedalTaskContainer(),
                _ => new GuideTaskContainer()
            };
        }

        /// <summary>
        /// 创建任务节点
        /// </summary>
        private TaskNode CreateTaskNode(TaskContainer container, long taskId)
        {
            var taskNode = new TaskNode(taskId, taskId);
            container.AddChildNode(taskNode);
            Print($"Created TaskNode for task {taskId}");
            return taskNode;
        }

        /// <summary>
        /// 创建子任务节点
        /// </summary>
        private SubTaskNode CreateSubTaskNode(TaskNode taskNode, long taskId)
        {
            var subTaskNode = new SubTaskNode(taskId, taskId);
            taskNode.AddChildNode(subTaskNode);
            Print($"Created SubTaskNode for task {taskId}");
            return subTaskNode;
        }

        /// <summary>
        /// 测试初始计数
        /// </summary>
        private void TestInitialCount(TaskNode taskNode, SubTaskNode subTaskNode)
        {
            Print("--- Testing Initial Count ---");

            // 验证初始计数为0
            Assert(subTaskNode.Count == 0, $"SubTaskNode initial count should be 0, but got {subTaskNode.Count}");
            Assert(taskNode.Count == 0, $"TaskNode initial count should be 0, but got {taskNode.Count}");

            Print($"✓ Initial counts verified - SubTask: {subTaskNode.Count}, Task: {taskNode.Count}");
        }

        /// <summary>
        /// 测试计数更新
        /// </summary>
        private void TestCountUpdate(TaskNode taskNode, SubTaskNode subTaskNode)
        {
            Print("--- Testing Count Update ---");

            // 设置子任务计数
            subTaskNode.Count = 5;

            // 验证计数更新
            Assert(subTaskNode.Count == 5, $"SubTaskNode count should be 5, but got {subTaskNode.Count}");
            Assert(taskNode.Count == 5, $"TaskNode count should be 5, but got {taskNode.Count}");

            Print($"✓ Count update verified - SubTask: {subTaskNode.Count}, Task: {taskNode.Count}");
        }

        /// <summary>
        /// 测试计数累加
        /// </summary>
        private void TestCountAccumulation(TaskNode taskNode, SubTaskNode subTaskNode)
        {
            Print("--- Testing Count Accumulation ---");

            // 增加计数
            subTaskNode.Count += 3;

            // 验证计数累加
            Assert(subTaskNode.Count == 8, $"SubTaskNode count should be 8, but got {subTaskNode.Count}");
            Assert(taskNode.Count == 8, $"TaskNode count should be 8, but got {taskNode.Count}");

            Print($"✓ Count accumulation verified - SubTask: {subTaskNode.Count}, Task: {taskNode.Count}");
        }

        /// <summary>
        /// 测试任务完成状态
        /// </summary>
        private void TestTaskCompletion(TaskNode taskNode, SubTaskNode subTaskNode, McCommon.Tables.QuestPhase taskConfig)
        {
            Print("--- Testing Task Completion ---");

            // 获取任务完成条件
            var conditionConfig = McCommon.Tables.TbQuestCondition.GetOrDefault(taskConfig.TaskPhaseEndCondition);
            if (conditionConfig != null && taskConfig.EndConditionParameter.Length > 0)
            {
                var targetCount = (int)taskConfig.EndConditionParameter[0];
                Print($"Task target count: {targetCount}");

                // 设置计数达到目标值
                subTaskNode.Count = targetCount;

                // 验证计数正确
                Assert(subTaskNode.Count == targetCount, $"SubTaskNode count should be {targetCount}, but got {subTaskNode.Count}");
                Assert(taskNode.Count == targetCount, $"TaskNode count should be {targetCount}, but got {taskNode.Count}");

                Print($"✓ Task completion count verified - SubTask: {subTaskNode.Count}, Task: {taskNode.Count}");
            }
            else
            {
                Print("No completion condition found for this task");
            }
        }

        /// <summary>
        /// 断言方法
        /// </summary>
        private void Assert(bool condition, string message)
        {
            if (!condition)
            {
                Print($"❌ ASSERTION FAILED: {message}");
                throw new Exception($"Test assertion failed: {message}");
            }
        }
    }
}
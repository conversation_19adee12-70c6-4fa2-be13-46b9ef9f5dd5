using Gameserver;
using MessagePack;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Config;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Team;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.ClassImpl.Entity.Player;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.Framework.Network;
using WizardGames.Soc.SocWorld.Lobby;
using WizardGames.Soc.SocWorld.Util;
using WizardGames.Soc.SocWorld.WorldCommon;

namespace WizardGames.Soc.Common.Entity
{
    [HotfixClass]
    public static partial class TeamEntityHotfix
    {

        [Hotfix]
        public static void Init(this TeamEntity self)
        {
            self.MemberDic = new();
            self.TeamBornInfo = new();
            self.TeamReputationInfo = new();
            self.RecruitmentApplication = new();
            self.VoteRoles = new();
            self.RecruitmentInfo = new();

            if (FunctionSwitchComponent.Instance.IsEnable(FunctionConst.BeebuzzTask))
            {
                self.AddComponent(new BeeBuzzComponent());
            }

            TeamManagerEntity.Instance.AllTeamId.Add(self.EntityId);
        }

        [Hotfix]
        public static void InitFromDb(this TeamEntity self)
        {
            foreach (var (roleId, memberInfo) in self.MemberDic)
            {
                if (memberInfo.EntityId > 0)
                {
                    self.JoinTeamAddEvent(memberInfo);
                }
            }
        }

        [Hotfix]
        public static void Cleanup(this TeamEntity self)
        {
            TeamManagerEntity.Instance.AllTeamId.Remove(self.EntityId);
        }

        public static int TeamReputationLevel(this TeamEntity self)
        {
            int level = 0;
            foreach (var playerEntity in self.GetPlayerMember())
            {
                var reputationComponent = playerEntity.GetComponent<ReputationComponent>(EComponentIdEnum.ReputationComponentId);
                if (reputationComponent.ReputationLevel > level)
                    level = reputationComponent.ReputationLevel;
            }
            return level;
        }

        public static TeamMemberInfo GetMemberInfo(this TeamEntity team, ulong roleId)
        {
            if (team.MemberDic.TryGetValue(roleId, out var teamInfo))
            {
                return teamInfo;
            }
            return null;
        }

        public static int FindAvailableTeamPosition(this TeamEntity self)
        {
            var maxTeamSize = GlobalInfoSyncEntity.Instance.TeamMemberLimit;
            Span<bool> positions = stackalloc bool[maxTeamSize];
            foreach (var (_, teamInfo) in self.MemberDic)
            {
                if (teamInfo.Position >= maxTeamSize) continue; // 万一策划改了表，读档就会跪了，所以就跳过之前分配的过大的值
                positions[teamInfo.Position] = true;
            }

            for (var i = 0; i < positions.Length; i++)
            {
                if (!positions[i]) return i;
            }
            self.Logger.Warn("FindAvailableTeamPosition fail, return default value");
            return maxTeamSize;
        }

        /// <summary>
        /// 获取到队伍中存在PlayerEntity的玩家列表
        /// </summary>
        public static IEnumerable<PlayerEntity> GetPlayerMember(this TeamEntity self)
        {
            if (self.MemberDic == null)
            {
                yield break;
            }
            foreach (var (roleId, _) in self.MemberDic)
            {
                var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
                if (player == null) continue;
                yield return player;
            }
        }

        public static void AddMemberWithDuty(this TeamEntity self, ulong roleId, int duty) => self.AddMember(roleId, duty, JoinTeamType.Normal);


        public static void AddMember(this TeamEntity self, ulong roleId) => self.AddMember(roleId, 0, JoinTeamType.Normal);

        [Hotfix]
        public static void AddMember(this TeamEntity self, ulong roleId, int duty, int joinType)
        {
            var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            var hasPlayer = player != null;
            if (!hasPlayer)
            {
                self.Logger.Info($"TeamAddMember: player {roleId} not found");
            }

            if (self.MemberDic.ContainsKey(roleId))
            {
                self.Logger.Error($"TeamAddMember: player {roleId} already in team {self.EntityId}");
                return;
            }

            if (hasPlayer)
            {
                EntityStaticCallback<PlayerEntity>.SyncInvokeStaticCallback(player, new BeforeJoinTeam(self.EntityId));
            }

            var memberInfo = new TeamMemberInfo()
            {
                RoleId = roleId,
                EntityId = hasPlayer ? player.EntityId : 0,
                EnterTime = ProcessEntity.Instance.NowTs,
                Duty = duty,
                JoinType = joinType,
                Position = self.FindAvailableTeamPosition(),
                TechnologyIds = hasPlayer ? new(player.ComponentBlueprint.GetTechnologyIds()) : new(),
                BlueprintIds = hasPlayer ? new(player.ComponentBlueprint.GetBlueprintIds()) : new(),
                EquipmentDisplayData = new(),
                WeaponDisplayData = new(),
                Costumes = hasPlayer ? new(PlayerUtil.GetAllCostumes(player)) : new(),
                CostumeOptions = hasPlayer ? new(PlayerUtil.GetAllCostumeOptions(player)) : new(),
                CallOnlineTime = new(),
            };

            self.MemberDic.Add(roleId, memberInfo);
            if (hasPlayer)
            {
                self.JoinTeamWithPlayerEntity(player);
                memberInfo.EquipmentDisplayData = player.ComponentInventory.GetEquipmentDisplayData();
                memberInfo.WeaponDisplayData = player.ComponentInventory.GetWeaponDisplayData();
            }

            EntityStaticCallback<TeamEntity>.SyncInvokeStaticCallback(self, new JoinTeamWithRoleId(roleId));
            self.BroadcastJoinTeam(roleId);
            if (!hasPlayer)
            {
                ServerInstanceEntity.Instance.PlaceHolderPlayer[roleId] = new HolderPlayerInfo() { TeamId = self.EntityId };
            }

            self.Logger.Info($"AddMember roleId {roleId} duty {duty} joinType {joinType}");
        }

        public static void JoinTeamWithPlayerEntity(this TeamEntity self, PlayerEntity player)
        {
            if (!self.MemberDic.TryGetValue(player.RoleId, out var memberInfo))
            {
                self.Logger.Error($"JoinTeamWithPlayerEntity: player {player.RoleId} not in team {self.EntityId}");
                return;
            }

            var playerTeamComponent = player.GetComponent<TeamComponent>(EComponentIdEnum.PlayerTeamComponentId);
            playerTeamComponent.TeamId = self.EntityId;
            player.SubscribeEntity(self.EntityId, false, TagCategory.TEAM);
            self.AddMemberForSubscribe(player);
            if (self.TeamReputationInfo.ReputationCabinetId != 0)
            {
                EntityStaticCallback<PlayerEntity>.InvokeStaticCallback(player, new FinishActionEvent(McCommon.Tables.TbQuestConst.ActivateReputation));
            }
            self.TeamReputationInfo.CalConversionEfficiency(self);
            memberInfo.EntityId = player.EntityId;
            if (self.CaptainRoleId == player.RoleId)
            {
                self.InviteOtherCount = playerTeamComponent.InviteOtherCount;
            }

            self.JoinTeamAddEvent(memberInfo);
            EntityStaticCallback<TeamEntity>.SyncInvokeStaticCallback(self, new JoinTeamWithPlayerEntity(player));
        }

        public static void JoinTeamAddEvent(this TeamEntity self, TeamMemberInfo info)
        {
            var entityId = info.EntityId;
            var eventSeq = self.ListenTargetEvent(entityId, OnWeaponDisplayDataChangeHotfix);
            if (eventSeq == EntityBase.INVALID_EVENT_SEQ)
            {
                self.Logger.Error($"JoinTeamAddEvent: player {info.RoleId} add event OnWeaponDisplayDataChange fail");
            }
            else
            {
                info.EventSeq.Add(eventSeq);
            }

            eventSeq = self.ListenTargetEvent(entityId, OnEquipmentDisplayDataChangeHotfix);

            if (eventSeq == EntityBase.INVALID_EVENT_SEQ)
            {
                self.Logger.Error($"JoinTeamAddEvent: player {info.RoleId} add event OnEquipmentDisplayDataChange fail");
            }
            else
            {
                info.EventSeq.Add(eventSeq);
            }

            eventSeq = self.ListenTargetEvent(entityId, UnlockTechCallbackHotfix);
            if (eventSeq == EntityBase.INVALID_EVENT_SEQ)
            {
                self.Logger.Error($"JoinTeamAddEvent: player {info.RoleId} add event UnlockTechCallbackHotfix fail");
            }
            else
            {
                info.EventSeq.Add(eventSeq);
            }

            eventSeq = self.ListenTargetEvent(entityId, UnlockBlueprintCallbackHotfix);
            if (eventSeq == EntityBase.INVALID_EVENT_SEQ)
            {
                self.Logger.Error($"JoinTeamAddEvent: player {info.RoleId} add event UnlockBlueprintCallbackHotfix fail");
            }
            else
            {
                info.EventSeq.Add(eventSeq);
            }
        }

        public static void LeaveTeamRemoveEvent(this TeamEntity self, TeamMemberInfo info)
        {
            foreach (var seq in info.EventSeq)
            {
                self.SafeRemoveEvent(seq);
            }
            info.EventSeq.Clear();
        }

        public static void AddMemberForSubscribe(this TeamEntity team, PlayerEntity player)
        {
            // 订阅队伍中的其他成员
            foreach (var memberEntity in team.GetPlayerMember())
            {
                if (memberEntity.RoleId != player.RoleId)
                {
                    memberEntity.SubscribeEntity(player.EntityId, true, TagCategory.TEAM);
                    player.SubscribeEntity(memberEntity.EntityId, true, TagCategory.TEAM);
                }
            }
        }

        public static void BroadcastJoinTeam(this TeamEntity team, ulong roleId)
        {
            if (roleId == team.CaptainRoleId) return;
            var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            var captain = UserManagerEntity.Instance.GetPlayerEntity(team.CaptainRoleId);
            if (player != null && captain != null)
            {
                player.RemoteCallPopMessageWithSingleParam(Framework.Network.ERpcTarget.OwnClient, 0, CommonTipConst.JoinTeamSuccessful, new Alpha3PopMsgParam(team.CaptainRoleId));
            }
            // 通知队伍成员
            team.BroadcastTeamInfo(CommonTipConst.JoinTeam, roleId);
        }


        public static void BroadcastTeamInfo(this TeamEntity team, int msgId, ulong targetRoleId)
        {
            foreach (var (roleId, _) in team.MemberDic)
            {
                if (roleId == targetRoleId)
                    continue;
                if (UserManagerEntity.Instance.GetPlayerEntity(roleId) is not PlayerEntity player) continue;
                if (player.CurrentNetPeerId > 0)
                    player.RemoteCallPopMessageWithSingleParam(ERpcTarget.OwnClient, 0, msgId, new Alpha3PopMsgParam(targetRoleId));
            }
        }

        public static void RemoveMember(this TeamEntity self, ulong roleId)
        {
            var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (!self.MemberDic.TryGetValue(roleId, out var memberInfo))
            {
                self.Logger.Warn($"TeamLeaveMember: player {roleId} not in team");
                return;
            }

            // 该事件调用时，玩家还没有从队伍中移除 before remove
            memberInfo.IsLeaving = true;
            EntityStaticCallback<TeamEntity>.SyncInvokeStaticCallback(self, new LeaveTeam(roleId));
            if (player != null)
            {
                player.ComponentTeam.TeamId = 0;
                player.UnsubscribeEntity(self.EntityId, false, TagCategory.TEAM);
                self.LeaveTeamForUnsubscibe(player);
                player.ComponentTeam.LeaveTeamForInvitation();
                self.LeaveTeamRemoveEvent(memberInfo);
            }

            self.MemberDic.Remove(roleId);
            self.MemberLeave(roleId, memberInfo);

            if (self.MemberDic.Count == 0 && !self.IsRemainedWhenNoMember)
            {
                self.DelayExecute(() =>
                {
                    EntityManager.Instance.RemoveEntity(self.EntityId);
                    TeamManagerEntity.Instance.RemoveRecruitment(self.EntityId);
                });
            }
            else
            {
                // 通知队伍成员
                self.BroadcastTeamInfo(4019, roleId);
                if (roleId == self.CaptainRoleId)
                {
                    self.OnCaptainLeaveTeam(roleId);
                }
            }

            if (player == null)
            {
                ServerInstanceEntity.Instance.PlaceHolderPlayer.Remove(roleId);
            }
            else
            {
                player.ComponentTeam.RemoteCallOnLeaveTeam(ERpcTarget.OwnClient);
            }

            self.Logger.Info($"RemoveMember roleId {roleId}");
        }

        public static void LeaveTeamForUnsubscibe(this TeamEntity team, PlayerEntity player)
        {
            // 取消订阅队伍中的其他成员
            foreach (var (memberId, _) in team.MemberDic)
            {
                if (memberId == player.RoleId) continue;
                var memberEntity = UserManagerEntity.Instance.GetPlayerEntity(memberId);
                if (memberEntity == null) continue;
                memberEntity.UnsubscribeEntity(player.EntityId, true, TagCategory.TEAM);
                player.UnsubscribeEntity(memberEntity.EntityId, true, TagCategory.TEAM);
            }
        }

        public static void OnCaptainLeaveTeam(this TeamEntity team, ulong roleId)
        {
            //转移队长权限 转移队长给 拥有权限 且 最早加入队伍的成员
            long minTimeStamp = long.MaxValue;
            TeamMemberInfo nextCaptain = null;
            TeamEntity.TempMemberInfos.Clear();
            foreach (var (id, info) in team.MemberDic)
            {
                if (info.Duty != 0)
                {
                    TeamEntity.TempMemberInfos.Add(info);
                }
                if (info.EnterTime < minTimeStamp)
                {
                    minTimeStamp = info.EnterTime;
                    nextCaptain = info;
                }
            }

            if (TeamEntity.TempMemberInfos.Count > 0)
            {
                TeamEntity.TempMemberInfos.Sort((a, b) => a.EnterTime.CompareTo(b.EnterTime));
                nextCaptain = TeamEntity.TempMemberInfos[0];
            }

            //提示：您已成为队长
            var playerCol = UserManagerEntity.Instance.GetPlayerEntity(nextCaptain.RoleId);
            if (playerCol != null)
            {
                playerCol.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.TeamCaptain);
            }
            team.CaptainRoleId = nextCaptain.RoleId;
            nextCaptain.Duty = TeamMemberDuty.TeamLeaderDutyValue;
            team.OnCaptainChange(roleId, team.CaptainRoleId);
            TeamEntity.TempMemberInfos.Clear();

        }

        public static void AddTeamCombatMarker(this TeamEntity team, long newMarkerId, MarkerBase marker, ulong fromUid)
        {
            foreach (var (uid, _) in team.MemberDic)
            {
                if (uid == fromUid) continue;
                var pc = UserManagerEntity.Instance.GetPlayerEntity(uid);
                if (pc == null) continue;
                pc.ComponentMark.TeamMemberAddCombatMarker(newMarkerId, marker);
            }
        }

        public static void RemoveTeamCombatMarker(this TeamEntity team, long markerId, ulong fromUid)
        {
            foreach (var (uid, _) in team.MemberDic)
            {
                if (uid == fromUid) continue;
                var pc = UserManagerEntity.Instance.GetPlayerEntity(uid);
                if (pc == null) continue;
                var markerComponent = pc.GetComponent<PlayerMarkComponent>(Component.EComponentIdEnum.PlayerMarkComponentId);
                markerComponent.TeamRemoveCombatMarker(markerId);
            }
        }

        public static void ModifyTeamPositionMarker(this TeamEntity team, long markerId, float x, float y, float z, ulong fromUid)
        {
            foreach (var (uid, _) in team.MemberDic)
            {
                if (uid == fromUid) continue;
                var pc = UserManagerEntity.Instance.GetPlayerEntity(uid);
                if (pc == null) continue;
                var markerComponent = pc.GetComponent<PlayerMarkComponent>(Component.EComponentIdEnum.PlayerMarkComponentId);
                markerComponent.TeamModifyPositionCombatMarker(markerId, x, y, z);
            }
        }

        public static void TeammateConfirmMarker(this TeamEntity team, long markerId, ulong fromUid)
        {
            foreach (var (uid, _) in team.MemberDic)
            {
                if (uid == fromUid) continue;
                var pc = UserManagerEntity.Instance.GetPlayerEntity(uid);
                if (pc == null) continue;
                var markerComponent = pc.GetComponent<PlayerMarkComponent>(Component.EComponentIdEnum.PlayerMarkComponentId);
                markerComponent.TeammateConfirmMarker(markerId, fromUid);
            }
        }

        public static void TeammateCancelConfirmMarker(this TeamEntity team, long markerId, ulong fromUid)
        {
            foreach (var (uid, _) in team.MemberDic)
            {
                if (uid == fromUid) continue;
                var pc = UserManagerEntity.Instance.GetPlayerEntity(uid);
                if (pc == null) continue;
                var markerComponent = pc.GetComponent<PlayerMarkComponent>(Component.EComponentIdEnum.PlayerMarkComponentId);
                markerComponent.TeammateCancelConfirmMarker(markerId, fromUid);
            }
        }

        public static MarkerBase FindOriginMarker(this TeamEntity team, long markerId, ulong fromUid)
        {
            foreach (var (uid, _) in team.MemberDic)
            {
                if (uid == fromUid) continue;
                var pc = UserManagerEntity.Instance.GetPlayerEntity(uid);
                if (pc == null) continue;
                var markerComponent = pc.GetComponent<PlayerMarkComponent>(Component.EComponentIdEnum.PlayerMarkComponentId);
                if (markerComponent.GetOriginMarker(markerId) is MarkerBase mb) return mb;
            }
            return null;
        }

        public static void OnCaptainChange(this TeamEntity self, ulong oldCaptain, ulong newCaptain)
        {
            TLogUtil.LogTeamChangeEvent(oldCaptain, self.EntityId, "member", self.MemberDic.Count, 0, null);
            TLogUtil.LogTeamChangeEvent(newCaptain, self.EntityId, "captain", self.MemberDic.Count, 1, null);

            // 队长变更 没收其他人的所有权限
            foreach (var (roleId, memberInfo) in self.MemberDic)
            {
                if (roleId == newCaptain) continue;
                // 没有权限 不需要没收
                if (memberInfo.Duty == 0) continue;
                memberInfo.Duty = 0;
                var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
                if (player != null && player.CurrentNetPeerId > 0)
                {
                    player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.LosePartyPermissions);
                }
            }

            // 队长发生变化 招募失效
            self.RecruitmentInfo.LastRecruitTime = 0;
            self.RecruitmentInfo.RecruitVersion++;
            self.CanShareRecruit = false;
            // 队长发生改变，所有招募申请都失效
            self.RecruitmentApplication.Clear();
            // 队长发生变化，邀请次数也改变
            var newCaptainPlayer = UserManagerEntity.Instance.GetPlayerEntity(newCaptain);
            self.InviteOtherCount = newCaptainPlayer?.ComponentTeam.InviteOtherCount ?? 0;
            if (TeamManagerEntity.Instance.RemoveRecruitment(self.EntityId))
            {
                if (newCaptainPlayer != null && newCaptainPlayer.CurrentNetPeerId > 0)
                {
                    // 队长变更后，通知新队长招募信息已失效
                    newCaptainPlayer.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, 24310);
                }
            }
        }

        [Hotfix(NeedWrapper = true)]
        public static void OnPlayerReborn(this TeamEntity self, ulong roleId)
        {
            var playerCol = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (playerCol == null)
            {
                self.Logger.Error($"OnPlayerReborn: player {roleId} not found");
                return;
            }
            self.BroadcastTeamInfo(CommonTipConst.TeammateRevive, roleId);
        }


        public static int GetJoinType(this TeamEntity team, ulong roleId)
        {
            if (team.MemberDic.TryGetValue(roleId, out var member))
                return member.JoinType;
            return 0;
        }

        public static ulong GetCaptainRoleId(this TeamEntity team) => team.CaptainRoleId;


        public static void SetTeamBornInfo(this TeamEntity team, Vector3 bornPositon)
        {
            team.TeamBornInfo.IsInit = true;
            team.TeamBornInfo.BornPosition.X = bornPositon.X;
            team.TeamBornInfo.BornPosition.Y = bornPositon.Y;
            team.TeamBornInfo.BornPosition.Z = bornPositon.Z;
        }

        public static bool HasPrivilegeForTeam(this TeamEntity self, ulong roleId, int duty)
        {
            var memberInfo = self.GetMemberInfo(roleId);
            if (memberInfo == null)
            {
                self.Logger.Warn($"HasPrivilegeForTeam memberInfo is null");
                return false;
            }

            return ByteUtil.HasFlag(memberInfo.Duty, duty);
        }

        [Hotfix(EventCallback = true)]
        public static void OnWeaponDisplayDataChange(this TeamEntity self, PlayerWeaponChangeEvent evt)
        {
            if (UserManagerEntity.Instance.GetPlayerEntity(evt.RoleId) is PlayerEntity player && self.GetMemberInfo(player.RoleId) is TeamMemberInfo info)
            {
                info.WeaponDisplayData = player.ComponentInventory.GetWeaponDisplayData();
            }
        }

        [Hotfix(EventCallback = true)]
        public static void OnEquipmentDisplayDataChange(this TeamEntity self, PlayerEquipmentChangeEvent evt)
        {
            if (UserManagerEntity.Instance.GetPlayerEntity(evt.RoleId) is PlayerEntity player && self.GetMemberInfo(player.RoleId) is TeamMemberInfo info)
            {
                info.EquipmentDisplayData = player.ComponentInventory.GetEquipmentDisplayData();
            }
        }
        public static PlayerDisplayData GetPlayerDisplayData(this TeamEntity team, ulong roleId)
        {
            if (team.MemberDic.TryGetValue(roleId, out var memberInfo))
            {
                var playerDisplayData = new PlayerDisplayData(UserManagerEntity.Instance.GetPlayerEntity(roleId));
                if (memberInfo.EquipmentDisplayData != null)
                {
                    foreach (var item in memberInfo.EquipmentDisplayData)
                    {
                        playerDisplayData.EquipmentDisplayDatas.Add(new EquipmentDisplayData(item));
                    }
                }
                if (memberInfo.WeaponDisplayData != null)
                {
                    foreach (var item in memberInfo.WeaponDisplayData)
                    {
                        playerDisplayData.WeaponDisplayDatas.Add(new WeaponDisplayData(item));
                    }
                }
                return playerDisplayData;
            }
            return null;
        }

        public static void ApplyJoinBattleRecruitment(this TeamEntity self, long seq, ApplyJoinBattleRecruitmentReq req)
        {
            var rsp = new ApplyJoinBattleRecruitmentRsp() { Success = false };
            var teamId = self.EntityId;
            // 战局服人数已满
            if (UserManagerEntity.Instance.IsPlayerOverServerLimit())
            {
                self.Logger.Info($"ApplyJoinBattleRecruitment server player limit");
                rsp.MsgID = CommonTipConst.SendServerIsFull;
                GateRpcHandler.OnApplyJoinBattleRecruitment(seq, rsp);
                return;
            }

            var roleId = req.ApplicantID;
            // 在战局内已经有其他队伍
            var teamIdInBattle = TeamUtil.GetTeamId(roleId);
            if (teamIdInBattle != 0 && teamIdInBattle != teamId)
            {
                self.Logger.Info($"ApplyJoinBattleRecruitment player {roleId} teamId {teamId} already int other team {teamIdInBattle}");
                rsp.MsgID = CommonTipConst.RecruitmentLapses;
                GateRpcHandler.OnApplyJoinBattleRecruitment(seq, rsp);
                return;
            }

            // 申请方已经是队伍成员
            if (teamIdInBattle != 0 && teamIdInBattle == teamId)
            {
                self.Logger.Info($"ApplyJoinBattleRecruitment player {roleId} already in team {teamId}");
                rsp.MsgID = CommonTipConst.TargetAlreadyInTeam;
                GateRpcHandler.OnApplyJoinBattleRecruitment(seq, rsp);
                return;
            }

            // 招募信息已经失效了
            if (self.RecruitmentInfo.RecruitVersion != req.RecruitVersion)
            {
                self.Logger.Info($"ApplyJoinBattleRecruitment player {roleId} recruitment expired {self.RecruitmentInfo.RecruitVersion} != {req.RecruitVersion}");
                rsp.MsgID = CommonTipConst.RecruitmentLapses;
                GateRpcHandler.OnApplyJoinBattleRecruitment(seq, rsp);
                return;
            }

            // 接受招募信息已达上限
            var recruitLimit = McCommon.Tables.TbGlobalConfig.SendRecruitmentApplyForLimit;
            if (self.RecruitmentApplication.Count >= recruitLimit)
            {
                self.Logger.Info($"ApplyJoinBattleRecruitment player {roleId} recruitment limit");
                // 对方的队伍太热门了，请选择其他队伍试试
                rsp.MsgID = 4052;
                GateRpcHandler.OnApplyJoinBattleRecruitment(seq, rsp);
                return;
            }

            if (!string.IsNullOrEmpty(req.JoinRemark) && Encoding.UTF8.GetByteCount(req.JoinRemark) > TeamComponentHotfix.TEAM_APPLY_REMARK_MAX_LENGTH)
            {
                self.Logger.Info($"ApplyJoinBattleRecruitment player {roleId} join remark too long");
                GateRpcHandler.OnApplyJoinBattleRecruitment(seq, rsp);
                return;
            }

            // 发布招募的人已经没有跨服邀请次数了
            var recruiter = UserManagerEntity.Instance.GetPlayerEntity(self.RecruitmentInfo.RecruitRole);
            if (recruiter != null && recruiter.ComponentTeam.GetInviteOtherCount() <= 0)
            {
                self.Logger.Info($"ApplyJoinBattleRecruitment player {roleId} recruiter {self.RecruitmentInfo.RecruitRole} no invite count");
                // 对方无权限招募非战局内玩家进行游戏
                rsp.MsgID = 4076;
                GateRpcHandler.OnApplyJoinBattleRecruitment(seq, rsp);
                return;
            }

            if (req.JoinRemark == "" || ServerConfig.Instance.EnableMinganci != 1)
            {
                self.AddTeamRecruitmentApplication(roleId, (int)req.SourceID, req.SourceRemark, req.JoinRemark);
                rsp.Success = true;
                GateRpcHandler.OnApplyJoinBattleRecruitment(seq, rsp);
            }
            else
            {
                var remark = req.JoinRemark;
                int source = (int)req.SourceID;
                string sourceRemark = req.SourceRemark;

                // 检查敏感词
                ServerLobbyServiceHotfix.MinganciTest(LobbyServiceManager.Instance.lobbyService, remark, MinganciSceneId.TEAM_RECRUIT_APPLY, roleId, (content, statusCode, errorCode) =>
                {
                    var checkResult = content["checkResult"].AsInt;
                    if (statusCode != HttpStatusCode.OK || errorCode != 0 || checkResult != 0)
                    {
                        self.Logger.Error($"recruit teamStatement minganci check fail teamName {remark} httpStatus {statusCode} errorCode {errorCode} checkResult:{checkResult}");
                        rsp.MsgID = 6003;
                        GateRpcHandler.OnApplyJoinBattleRecruitment(seq, rsp);
                        return;
                    }
                    remark = content["filterText"];
                    self.AddTeamRecruitmentApplication(roleId, source, sourceRemark, remark);
                    rsp.Success = true;
                    GateRpcHandler.OnApplyJoinBattleRecruitment(seq, rsp);
                });
            }
        }

        public static void AddTeamRecruitmentApplication(this TeamEntity self, ulong roleId, int source, string sourceRemark, string remark)
        {
            if (!self.RecruitmentApplication.TryGetValue(roleId, out var applicationInfo))
            {
                applicationInfo = new TeamRecruitmentApplicationInfo()
                {
                    Source = source,
                    JoinRemark = remark,
                    SourceRemark = sourceRemark,
                    ApplyTime = ProcessEntity.Instance.NowTs,
                };
                self.RecruitmentApplication.Add(roleId, applicationInfo);
            }
            else
            {
                applicationInfo.Source = source;
                applicationInfo.JoinRemark = remark;
                applicationInfo.SourceRemark = sourceRemark;
                applicationInfo.ApplyTime = ProcessEntity.Instance.NowTs;
            }
            var captain = UserManagerEntity.Instance.GetPlayerEntity(self.CaptainRoleId);
            if (captain != null && captain.CurrentNetPeerId > 0)
            {
                using var disposableArray = CustomTypeHelper.GetDisposableArrayWriter();
                MessagePackWriter writer = new(disposableArray);
                applicationInfo.SerializeCore(ref writer, ESerializeMode.OwnClient, 0);
                writer.Flush();
                captain.ComponentTeam.RemoteCallNewRecruitmentApplicationNotify(ERpcTarget.OwnClient, roleId, disposableArray.GetSequence());
            }
        }

        public static void RemoveTeamRecruitmentApplication(this TeamEntity self, ulong roleId)
        {
            self.RecruitmentApplication.Remove(roleId);
        }

        [Hotfix(EventCallback = true)]
        public static void UnlockTechCallback(this TeamEntity team, UnlockTechEvent unlockEvent)
        {
            if (team.MemberDic.TryGetValue(unlockEvent.RoleId, out var info))
            {
                info.TechnologyIds.Add(unlockEvent.BizId);

                foreach (var roleId in team.MemberDic.Keys)
                {
                    var playerEntity = UserManagerEntity.Instance.GetPlayerEntity(roleId);
                    if (null != playerEntity)
                    {
                        EntityStaticCallback<PlayerEntity>.InvokeStaticCallback(playerEntity, ShareTechnologyEvent.Instance);
                    }
                }
            }
        }

        [Hotfix(EventCallback = true)]
        public static void UnlockBlueprintCallback(this TeamEntity team, UnlockBlueprintEvent unlockEvent)
        {
            if (team.MemberDic.TryGetValue(unlockEvent.RoleId, out var info))
            {
                info.BlueprintIds.Add(unlockEvent.BizId);
            }
        }

        public static bool ChangeCaptain(this TeamEntity self, ulong roleId)
        {
            var currentCaptainRoleId = self.CaptainRoleId;
            if (currentCaptainRoleId == roleId)
            {
                self.Logger.Warn($"TeamHandoverCaptain captainRoleId {self.CaptainRoleId} already Captain");
                return false;
            }

            var currentCaptainInfo = self.GetMemberInfo(currentCaptainRoleId);
            if (currentCaptainInfo == null)
            {
                self.Logger.Warn($"TeamHandoverCaptain cannot find currentCaptainInfo {currentCaptainRoleId}");
                return false;
            }

            var targetInfo = self.GetMemberInfo(roleId);
            if (targetInfo == null)
            {
                self.Logger.Warn($"TeamHandoverCaptain cannot find targetInfo {roleId}");
                return false;
            }

            self.CaptainRoleId = roleId;
            targetInfo.Duty = currentCaptainInfo.Duty;
            currentCaptainInfo.Duty = 0;
            self.OnCaptainChange(currentCaptainRoleId, roleId);
            self.Logger.Info($"TeamHandoverCaptain {currentCaptainRoleId} {roleId} successfully");

            var toPlayer = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (toPlayer != null && toPlayer.CurrentNetPeerId > 0)
            {
                toPlayer.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.TeamCaptain);
            }
            return true;
        }

        public static void AddCheckCaptainImpeachTimer(this TeamEntity self)
        {
            uint timeout = (uint)McCommon.Tables.TbGlobalConfig.CaptainImpeachmentTime * 60 * 60 * 1000;
            self.ImpeachTimerId = self.AddArchiveTimerOnce(timeout, OnCheckCaptainImpeachHotfix);
        }

        [Hotfix(TimerCallback = true)]
        public static void OnCheckCaptainImpeach(this TeamEntity self, long timerId)
        {
            if (self.MemberDic.Count < 2) return;
            self.ImpeachState = (int)EImpeachState.Applying;
        }

        [Hotfix(TimerCallback = true)]
        public static void ResetVoteTimeout(this TeamEntity self, long timerId)
        {
            self.VoteTimerId = TimerWheelConst.INVALID_TIMER_ID;
            self.ResetImpeachStateToApplying();
        }

        public static void ImpeachChangeCaptain(this TeamEntity self, ulong roleId)
        {
            var oldCaptainRoleId = self.CaptainRoleId;
            // 弹劾成功 转移队长
            var ret = self.ChangeCaptain(roleId);
            // 转移领地权限
            if (ret)
            {
                var oldCaptainPlayer = UserManagerEntity.Instance.GetPlayerEntity(oldCaptainRoleId);
                if (oldCaptainPlayer != null)
                {
                    var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
                    oldCaptainPlayer.ConstructionComp.ChangeTerritoryOwner(player, true);
                }
            }

            self.ResetImpeachStateToNone();
        }

        private static void MemberLeave(this TeamEntity self, ulong roleId, TeamMemberInfo info)
        {
            self.VoteRoles.Remove(roleId);
            if (self.MemberDic.Count == 1)
            {
                self.ResetImpeachStateToNone();
            }
            else
            {
                if (self.ImpeachState == (int)EImpeachState.Voting)
                {
                    // 申请玩家退出 
                    if (self.ApplicationPlayer == roleId)
                    {
                        self.ResetImpeachStateToApplying();
                    }
                    else
                    {
                        // 其他队员退出
                        var ret = self.CheckVote();
                        if (ret == EOpCode.VoteSuccessfully)
                        {
                            self.ImpeachChangeCaptain(self.ApplicationPlayer);
                            self.Logger.Info($"MemberLeaveForImpeach: {roleId} leave team, impeach success, new captain {self.ApplicationPlayer}");
                        }
                        else if (ret == EOpCode.VoteFailed)
                        {
                            self.ResetImpeachStateToApplying();
                            self.Logger.Info($"MemberLeaveForImpeach: {roleId} leave team, impeach failed");
                        }
                        else
                        {
                            self.Logger.Info($"MemberLeaveForImpeach: {roleId} leave team, impeach ongoing, current state {self.ImpeachState} application player {self.ApplicationPlayer} vote roles count {self.VoteRoles.Count}");
                        }
                    }
                }
            }

            if (info.CheckTerritoryTimerId != TimerWheelConst.INVALID_TIMER_ID)
            {
                self.SafeCancelTimerNonRef(info.CheckTerritoryTimerId);
                info.CheckTerritoryTimerId = TimerWheelConst.INVALID_TIMER_ID;
            }
        }

        public static void ResetImpeachStateToNone(this TeamEntity self)
        {
            self.ApplicationPlayer = 0;
            self.VoteRoles.Clear();
            self.ImpeachState = (int)EImpeachState.None;
            if (self.VoteTimerId != TimerWheelConst.INVALID_TIMER_ID)
            {
                self.SafeCancelTimerNonRef(self.VoteTimerId);
                self.VoteTimerId = TimerWheelConst.INVALID_TIMER_ID;
            }

            if (self.ImpeachTimerId != TimerWheelConst.INVALID_TIMER_ID)
            {
                self.SafeCancelTimerNonRef(self.ImpeachTimerId);
                self.ImpeachTimerId = TimerWheelConst.INVALID_TIMER_ID;
            }
        }

        public static void ResetImpeachStateToApplying(this TeamEntity self)
        {
            self.ApplicationPlayer = 0;
            self.VoteRoles.Clear();
            self.ImpeachState = (int)EImpeachState.Applying;
            if (self.VoteTimerId != TimerWheelConst.INVALID_TIMER_ID)
            {
                self.SafeCancelTimerNonRef(self.VoteTimerId);
                self.VoteTimerId = TimerWheelConst.INVALID_TIMER_ID;
            }
        }

        public static EOpCode CheckVote(this TeamEntity self)
        {
            var memberCount = self.MemberDic.Count;
            if (memberCount < 2)
            {
                self.Logger.Warn($"CheckVote: team {self.EntityId} member count is less than 2, cannot vote");
                return EOpCode.VoteFailed;
            }

            var agreeCount = 0;
            var disagreeCount = 0;
            foreach (var (roleId, agree) in self.VoteRoles)
            {
                if (agree)
                {
                    agreeCount++;
                }
                else
                {
                    disagreeCount++;
                }
            }

            // 至少一半同意或不同意
            if (agreeCount >= (memberCount + 1) / 2)
            {
                return EOpCode.VoteSuccessfully;
            }
            // 不同意超出半数 或者 能投票的人都投过票了还没有成功 那就算失败 3个人 除了队长 一人一票的情况
            else if (disagreeCount >= (memberCount + 1) / 2 || agreeCount + disagreeCount >= memberCount - 1)
            {
                return EOpCode.VoteFailed;
            }
            else
            {
                return EOpCode.StillNeedVote;
            }
        }

        [Hotfix(TimerCallback = true)]
        public static void CheckTerritoryTimeout(this TeamEntity self, long timerId, TimerULongParam roleParam)
        {
            var roleId = roleParam.Value;
            if (self.MemberDic.TryGetValue(roleId, out var memberInfo))
            {
                // 如果该玩家在线，则不需要转移领地权限
                var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
                if (player != null && player.CurrentNetPeerId > 0)
                {
                    self.Logger.Info($"CheckTerritoryTimeout: player {roleId} is online, no need to transfer territory");
                    return;
                }

                var captainRoleId = self.CaptainRoleId;
                if (roleId == captainRoleId)
                {
                    self.Logger.Info($"CheckTerritoryTimeout: player {roleId} is captain, no need to transfer territory");
                    return;
                }

                if (self.MemberDic.Count == 1)
                {
                    self.Logger.Info($"CheckTerritoryTimeout: player {roleId} is the only member, no need to transfer territory");
                    return;
                }

                // 转移领地权限给队长
                if (player != null)
                {
                    var captainPlayer = UserManagerEntity.Instance.GetPlayerEntity(captainRoleId);
                    if (captainPlayer != null)
                    {
                        player.ConstructionComp.ChangeTerritoryOwner(captainPlayer, false);
                        self.Logger.Info($"CheckTerritoryTimeout: player {roleId} territory transferred to captain {captainRoleId}");
                    }
                }
                memberInfo.CheckTerritoryTimerId = TimerWheelConst.INVALID_TIMER_ID;
            }
            else
            {
                self.Logger.Warn($"CheckTerritoryTimeout: player {roleId} not in team");
            }
        }
    }
}
